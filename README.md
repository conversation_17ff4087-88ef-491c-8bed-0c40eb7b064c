# MSRRMS - Municipal Supply Request and Release Management System

## 📋 Table of Contents
- [System Overview](#system-overview)
- [User Accounts](#user-accounts)
- [Login Instructions](#login-instructions)
- [System Features](#system-features)
- [Installation & Setup](#installation--setup)
- [Troubleshooting](#troubleshooting)
- [Support](#support)

## 🏛️ System Overview

The Municipal Supply Request and Release Management System (MSRRMS) is a comprehensive web-based application designed to streamline supply management for municipal government offices. The system enables efficient request submission, approval workflows, and inventory management across all municipal departments.

### Key Benefits
- **Centralized Supply Management**: All requests flow through a single system
- **Real-time Tracking**: Monitor request status from submission to release
- **Inventory Control**: Automated stock level monitoring and alerts
- **Audit Trail**: Complete history of all transactions and approvals
- **Modern Interface**: Responsive design works on desktop and mobile devices

## 👥 User Accounts

### 🔧 GSO (General Services Office) Administrator
**Full system access with approval and inventory management capabilities**

| Username | Password | Role | Department |
|----------|----------|------|------------|
| `gso_admin` | `gso123` | GSO Administrator | General Services Office |

**GSO Capabilities:**
- Approve/reject supply requests
- Manage inventory levels
- Generate comprehensive reports
- Monitor all department activities
- Release approved items to departments
- Add/edit supply items
- View system-wide analytics

---

### 🏢 Municipal Department Accounts
**All department accounts use password: `municipal123`**

#### Executive & Administrative Offices
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `mayor` | Mayor's Office | Municipal Mayor |
| `vicemayor` | Vice Mayor's Office | Vice Mayor & Sangguniang Bayan |
| `admin_office` | Municipal Administrator's Office | Municipal Administrator |
| `mpdo` | Municipal Planning and Development Office | Planning Officer |

#### Financial Management Offices
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `treasurer` | Municipal Treasurer's Office | Municipal Treasurer |
| `accountant` | Municipal Accountant's Office | Municipal Accountant |
| `budget` | Municipal Budget Office | Budget Officer |
| `assessor` | Municipal Assessor's Office | Municipal Assessor |

#### Public Service Offices
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `civilreg` | Municipal Civil Registrar's Office | Civil Registrar |
| `meo` | Municipal Engineering Office | Municipal Engineer |
| `mho` | Municipal Health Office | Health Officer |

#### Social & Environmental Services
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `mswdo` | Municipal Social Welfare and Development Office | Social Worker |
| `menro` | Municipal Environment and Natural Resources Office | Environment Officer |
| `oma` | Office of the Municipal Agriculturist | Municipal Agriculturist |

#### Business & Employment Services
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `bplo` | Business Permit & Licensing Office | Business Officer |
| `hrmo` | Human Resource Management Office | HR Manager |
| `peso` | Public Employment Service Office | Employment Officer |

#### Emergency & Development Services
| Username | Department | Head/Contact |
|----------|------------|--------------|
| `mdrrmo` | Municipal Disaster Risk Reduction and Management Office | Disaster Officer |
| `meedo` | Municipal Economic Enterprise Development Office | Economic Officer |

**Department User Capabilities:**
- Submit supply requests
- Track request status and history
- View available inventory
- Manage profile information
- Access department-specific dashboard

## 🔐 Login Instructions

### Step 1: Access the System
1. Open your web browser
2. Navigate to the MSRRMS login page: `http://your-server-address/login/`
3. You will see the modern MSRRMS login interface

### Step 2: Enter Credentials
1. **Username**: Enter your assigned username (see tables above)
2. **Password**: Enter your password
   - Department users: `municipal123`
   - GSO admin: `gso123`
3. Click the "Sign In" button

### Step 3: Dashboard Access
- **Department Users**: Redirected to department dashboard
- **GSO Administrator**: Redirected to GSO management dashboard

### First-Time Login Recommendations
1. **Change Your Password**: Go to Profile → Change Password
2. **Update Contact Information**: Verify your phone number and email
3. **Explore the Dashboard**: Familiarize yourself with available features

## 🚀 System Features

### For Department Users

#### 📝 Supply Request Management
- **Create Requests**: Submit requests for office supplies, equipment, and materials
- **Batch Requests**: Select multiple items in a single requisition
- **Search & Filter**: Find items quickly using advanced search functionality
- **Real-time Tracking**: Monitor request status from submission to release
- **Request History**: View complete history of all submitted requests

#### 📊 Dashboard Features
- **Quick Actions**: Fast access to create requests and view history
- **Statistics Overview**: Visual summary of request statuses
- **Recent Activity**: Latest request updates and notifications
- **Profile Management**: Update personal and department information

#### 🔍 Advanced Search Capabilities
- **Item Search**: Search by name, description, or category
- **Category Filtering**: Filter by supply categories
- **Stock Indicators**: View current inventory levels
- **Autocomplete**: Smart suggestions for faster item selection

### For GSO Administrator

#### ✅ Request Management
- **Approval Workflow**: Review and approve/reject department requests
- **Batch Processing**: Handle multiple requests simultaneously
- **Priority Management**: Set request priorities and urgency levels
- **Release Management**: Control item release to departments

#### 📦 Inventory Management
- **Stock Monitoring**: Real-time inventory level tracking
- **Low Stock Alerts**: Automatic notifications for items below minimum levels
- **Item Management**: Add, edit, and categorize supply items
- **Stock Adjustments**: Update inventory levels and perform audits

#### 📈 Reporting & Analytics
- **Department Reports**: Usage patterns by department
- **Inventory Reports**: Stock levels and movement analysis
- **Request Analytics**: Approval rates and processing times
- **Export Functionality**: Generate PDF and Excel reports

## 📦 Supply Inventory

The system includes **84 pre-loaded supply items** across 6 categories:

### Office Supplies (26 items)
Bond paper, writing instruments, office accessories, filing supplies

### Computer Supplies (10 items)
Printer cartridges, storage media, computer accessories

### Cleaning Supplies (15 items)
Sanitation products, cleaning tools, waste management supplies

### Medical Supplies (9 items)
First aid supplies, personal protective equipment, health items

### Maintenance Supplies (14 items)
Electrical supplies, tools, repair materials

### Furniture & Equipment (10 items)
Office furniture, electronic equipment, appliances

## ⚙️ Installation & Setup

### Prerequisites
- Python 3.8+
- Django 4.2+
- SQLite (default) or PostgreSQL
- Modern web browser

### Quick Setup
```bash
# Clone the repository
git clone <repository-url>
cd msrrms

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Populate municipal data
python manage.py populate_municipal_data

# Start the server
python manage.py runserver
```

### Data Population
The `populate_municipal_data` management command creates:
- 19 municipal department accounts
- 1 GSO administrator account
- 84 supply items with realistic stock levels

```bash
# Populate all data
python manage.py populate_municipal_data

# Delete existing GSO and recreate
python manage.py populate_municipal_data --delete-gso
```

## 🔧 Troubleshooting

### Common Issues

#### Login Problems
**Issue**: Cannot login with provided credentials
**Solution**: 
1. Verify username and password are correct
2. Check if account exists: `python manage.py shell -c "from django.contrib.auth.models import User; print([u.username for u in User.objects.all()])"`
3. Reset password if needed

#### Permission Errors
**Issue**: "You don't have permission to access this page"
**Solution**:
1. Verify user role in admin panel
2. Check UserProfile exists for the user
3. Ensure proper role assignment (GSO vs DEPARTMENT)

#### Missing Data
**Issue**: No supply items or departments visible
**Solution**:
1. Run data population command: `python manage.py populate_municipal_data`
2. Check database connectivity
3. Verify migrations are applied: `python manage.py migrate`

#### HTMX Not Working
**Issue**: Search and filters not updating dynamically
**Solution**:
1. Check browser console for JavaScript errors
2. Verify HTMX library is loaded
3. Clear browser cache and reload

### Database Issues
```bash
# Reset database (WARNING: This deletes all data)
rm db.sqlite3
python manage.py migrate
python manage.py populate_municipal_data

# Check database integrity
python manage.py check
```

### Performance Issues
- **Slow Loading**: Check database indexes and query optimization
- **Memory Usage**: Monitor Django debug toolbar for query analysis
- **Large Datasets**: Consider pagination and lazy loading

## 📞 Support

### System Administration
- **Database Management**: Use Django admin panel at `/admin/`
- **User Management**: Create/modify users through admin interface
- **System Logs**: Check Django logs for error tracking

### Development
- **Debug Mode**: Set `DEBUG = True` in settings for development
- **Static Files**: Run `python manage.py collectstatic` for production
- **Testing**: Use `python manage.py test` for automated testing

### Contact Information
For technical support or system issues:
- **System Administrator**: Contact your IT department
- **User Training**: Request training sessions for new users
- **Feature Requests**: Submit through proper channels

---

## 📄 License
This system is developed for municipal government use. All rights reserved.

## 🔄 Version History
- **v1.0**: Initial release with basic request management
- **v2.0**: Added modern UI and enhanced features
- **v2.1**: Implemented batch requests and advanced search

---

**Last Updated**: July 2025  
**System Version**: 2.1  
**Documentation Version**: 1.0
