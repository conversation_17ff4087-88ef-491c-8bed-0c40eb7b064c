from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from supply.models import UserProfile, SupplyItem, SupplyRequest, SupplyCategory
import random


class Command(BaseCommand):
    help = '''Populate the database with sample municipal data including:
    - Municipal department users with proper roles
    - Supply categories with color coding and icons
    - Comprehensive inventory items with categories, costs, suppliers, and locations
    - Sample supply requests

    Use --update-existing to update existing items with new category and field data.
    Use --delete-gso to remove existing GSO accounts before creating new ones.'''

    def add_arguments(self, parser):
        parser.add_argument(
            '--delete-gso',
            action='store_true',
            help='Delete existing GSO accounts',
        )
        parser.add_argument(
            '--update-existing',
            action='store_true',
            help='Update existing items with new category and field data',
        )

    def handle(self, *args, **options):
        # Delete existing GSO accounts if requested
        if options['delete_gso']:
            self.stdout.write('Deleting existing GSO accounts...')
            gso_users = User.objects.filter(userprofile__role='GSO')
            count = gso_users.count()
            gso_users.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {count} GSO accounts')
            )

        # Create municipal departments
        self.stdout.write('Creating municipal departments...')
        self.create_departments()

        # Create supply inventory
        self.stdout.write('Creating supply inventory...')
        self.create_supply_inventory(options)

        # Create new GSO account
        self.stdout.write('Creating new GSO account...')
        self.create_gso_account()

        self.stdout.write(
            self.style.SUCCESS('Successfully populated municipal data!')
        )

    def create_departments(self):
        """Create department users for all municipal offices"""
        departments = [
            ("Mayor's Office", "mayor", "Municipal", "Mayor"),
            ("Vice Mayor's Office", "vicemayor", "Vice", "Mayor"),
            ("Municipal Administrator's Office", "admin_office", "Municipal", "Administrator"),
            ("Municipal Planning and Development Office", "mpdo", "Planning", "Officer"),
            ("Municipal Civil Registrar's Office", "civilreg", "Civil", "Registrar"),
            ("Municipal Treasurer's Office", "treasurer", "Municipal", "Treasurer"),
            ("Municipal Accountant's Office", "accountant", "Municipal", "Accountant"),
            ("Municipal Budget Office", "budget", "Budget", "Officer"),
            ("Municipal Assessor's Office", "assessor", "Municipal", "Assessor"),
            ("Municipal Engineering Office", "meo", "Municipal", "Engineer"),
            ("Municipal Health Office", "mho", "Health", "Officer"),
            ("Municipal Social Welfare and Development Office", "mswdo", "Social", "Worker"),
            ("Municipal Environment and Natural Resources Office", "menro", "Environment", "Officer"),
            ("Office of the Municipal Agriculturist", "oma", "Municipal", "Agriculturist"),
            ("Business Permit & Licensing Office", "bplo", "Business", "Officer"),
            ("Human Resource Management Office", "hrmo", "HR", "Manager"),
            ("Public Employment Service Office", "peso", "Employment", "Officer"),
            ("Municipal Disaster Risk Reduction and Management Office", "mdrrmo", "Disaster", "Officer"),
            ("Municipal Economic Enterprise Development Office", "meedo", "Economic", "Officer"),
        ]

        for dept_name, username, first_name, last_name in departments:
            try:
                # Check if user already exists
                user, created = User.objects.get_or_create(
                    username=username,
                    defaults={
                        'email': f'{username}@municipality.gov.ph',
                        'first_name': first_name,
                        'last_name': last_name
                    }
                )

                if created:
                    user.set_password('municipal123')
                    user.save()
                    self.stdout.write(f'Created user: {username}')
                else:
                    self.stdout.write(f'User {username} already exists')

                # Check if profile exists
                profile, profile_created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'role': 'DEPARTMENT',
                        'department': dept_name,
                        'phone': f'09{random.randint(100000000, 999999999)}'
                    }
                )

                if profile_created:
                    self.stdout.write(f'Created profile for {username} - {dept_name}')
                else:
                    # Update existing profile
                    profile.department = dept_name
                    profile.role = 'DEPARTMENT'
                    profile.save()
                    self.stdout.write(f'Updated profile for {username} - {dept_name}')

            except Exception as e:
                self.stdout.write(f'Error processing user {username}: {str(e)}')

    def create_default_categories(self):
        """Create default supply categories if they don't exist"""
        default_categories = [
            {
                'name': 'Office Supplies',
                'description': 'General office supplies like paper, pens, folders',
                'color_code': '#3B82F6',
                'icon_class': 'fas fa-briefcase'
            },
            {
                'name': 'Cleaning Materials',
                'description': 'Cleaning supplies and maintenance materials',
                'color_code': '#10B981',
                'icon_class': 'fas fa-spray-can'
            },
            {
                'name': 'IT Equipment',
                'description': 'Computer hardware and technology equipment',
                'color_code': '#8B5CF6',
                'icon_class': 'fas fa-laptop'
            },
            {
                'name': 'Furniture',
                'description': 'Office furniture and fixtures',
                'color_code': '#F59E0B',
                'icon_class': 'fas fa-chair'
            },
            {
                'name': 'Medical Supplies',
                'description': 'First aid and medical supplies',
                'color_code': '#EF4444',
                'icon_class': 'fas fa-first-aid'
            },
            {
                'name': 'Stationery',
                'description': 'Writing materials and paper products',
                'color_code': '#06B6D4',
                'icon_class': 'fas fa-pen'
            },
        ]

        created_count = 0
        for cat_data in default_categories:
            category, created = SupplyCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                created_count += 1

        if created_count > 0:
            self.stdout.write(f'Created {created_count} default categories')
        else:
            self.stdout.write('Default categories already exist')

    def create_supply_inventory(self, options):
        """Create comprehensive supply inventory with categories and enhanced fields"""

        # First, ensure default categories exist
        self.create_default_categories()

        # Get category instances for assignment (with fallback if categories don't exist)
        categories = {}
        try:
            categories = {
                'Office Supplies': SupplyCategory.objects.get(name='Office Supplies'),
                'IT Equipment': SupplyCategory.objects.get(name='IT Equipment'),
                'Cleaning Materials': SupplyCategory.objects.get(name='Cleaning Materials'),
                'Medical Supplies': SupplyCategory.objects.get(name='Medical Supplies'),
                'Furniture': SupplyCategory.objects.get(name='Furniture'),
                'Stationery': SupplyCategory.objects.get(name='Stationery'),
            }
        except SupplyCategory.DoesNotExist:
            self.stdout.write(self.style.WARNING('Some categories not found, items will be created without categories'))

        # Enhanced supply data with additional fields: (name, unit, description, unit_cost, supplier, location)
        supply_categories = {
            'Office Supplies': [
                ('Bond Paper A4', 'ream', 'White bond paper 80gsm', 250.00, 'National Book Store', 'Storage Room A'),
                ('Bond Paper Legal', 'ream', 'White bond paper legal size', 280.00, 'National Book Store', 'Storage Room A'),
                ('Ballpoint Pen Blue', 'piece', 'Blue ink ballpoint pen', 15.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Ballpoint Pen Black', 'piece', 'Black ink ballpoint pen', 15.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Ballpoint Pen Red', 'piece', 'Red ink ballpoint pen', 15.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Pencil No. 2', 'piece', 'Standard No. 2 pencil', 8.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Eraser', 'piece', 'White rubber eraser', 5.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Ruler 12 inch', 'piece', 'Plastic ruler 12 inches', 25.00, 'Office Warehouse', 'Supply Cabinet 2'),
                ('Stapler', 'piece', 'Standard office stapler', 350.00, 'Office Depot', 'Equipment Storage'),
                ('Staple Wire', 'box', 'Staple wire No. 35', 45.00, 'Office Depot', 'Supply Cabinet 2'),
                ('Paper Clips', 'box', 'Standard paper clips', 35.00, 'Office Warehouse', 'Supply Cabinet 2'),
                ('Rubber Bands', 'pack', 'Assorted rubber bands', 25.00, 'Office Warehouse', 'Supply Cabinet 2'),
                ('Manila Folder', 'piece', 'Legal size manila folder', 12.00, 'National Book Store', 'Filing Area'),
                ('Expanding Folder', 'piece', 'Legal size expanding folder', 85.00, 'National Book Store', 'Filing Area'),
                ('Fastener', 'box', 'Metal fasteners', 55.00, 'Office Warehouse', 'Supply Cabinet 2'),
                ('Scotch Tape', 'piece', 'Transparent tape 1 inch', 65.00, 'Office Depot', 'Supply Cabinet 1'),
                ('Masking Tape', 'piece', 'Masking tape 1 inch', 45.00, 'Office Depot', 'Supply Cabinet 1'),
                ('Glue Stick', 'piece', 'Solid glue stick', 35.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Correction Fluid', 'piece', 'White correction fluid', 25.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Calculator', 'piece', 'Basic calculator', 450.00, 'Electronics Plus', 'Equipment Storage'),
                ('Clipboard', 'piece', 'Letter size clipboard', 125.00, 'Office Depot', 'Equipment Storage'),
            ],
            'IT Equipment': [
                ('Printer Ink Black', 'cartridge', 'Black ink cartridge', 1250.00, 'Tech Solutions', 'IT Storage'),
                ('Printer Ink Color', 'cartridge', 'Color ink cartridge', 1850.00, 'Tech Solutions', 'IT Storage'),
                ('Toner Cartridge', 'piece', 'Laser printer toner', 3500.00, 'Canon Philippines', 'IT Storage'),
                ('CD-R', 'piece', 'Blank CD-R disc', 25.00, 'Computer City', 'IT Storage'),
                ('DVD-R', 'piece', 'Blank DVD-R disc', 35.00, 'Computer City', 'IT Storage'),
                ('USB Flash Drive 16GB', 'piece', '16GB USB flash drive', 450.00, 'Tech Hub', 'IT Storage'),
                ('USB Flash Drive 32GB', 'piece', '32GB USB flash drive', 750.00, 'Tech Hub', 'IT Storage'),
                ('Mouse Pad', 'piece', 'Computer mouse pad', 85.00, 'Computer City', 'IT Storage'),
                ('Keyboard Cover', 'piece', 'Keyboard dust cover', 125.00, 'Tech Solutions', 'IT Storage'),
                ('Monitor Screen Cleaner', 'bottle', 'LCD screen cleaner', 185.00, 'Tech Hub', 'IT Storage'),
                ('Computer Mouse', 'piece', 'Optical computer mouse', 350.00, 'Tech Solutions', 'IT Storage'),
                ('Keyboard', 'piece', 'Standard computer keyboard', 650.00, 'Tech Solutions', 'IT Storage'),
            ],
            'Cleaning Materials': [
                ('Toilet Paper', 'roll', 'Toilet tissue paper', 45.00, 'Cleaning Supplies Co', 'Janitor Closet'),
                ('Hand Soap', 'bottle', 'Liquid hand soap', 85.00, 'Hygiene Plus', 'Janitor Closet'),
                ('Dishwashing Liquid', 'bottle', 'Dishwashing detergent', 65.00, 'Clean Pro', 'Janitor Closet'),
                ('Floor Wax', 'gallon', 'Floor wax polish', 450.00, 'Maintenance Supply', 'Janitor Closet'),
                ('Disinfectant', 'bottle', 'Multi-surface disinfectant', 125.00, 'Hygiene Plus', 'Janitor Closet'),
                ('Bleach', 'bottle', 'Chlorine bleach', 75.00, 'Clean Pro', 'Janitor Closet'),
                ('Trash Bags Large', 'pack', 'Large garbage bags', 185.00, 'Cleaning Supplies Co', 'Janitor Closet'),
                ('Trash Bags Small', 'pack', 'Small garbage bags', 125.00, 'Cleaning Supplies Co', 'Janitor Closet'),
                ('Broom', 'piece', 'Soft broom', 185.00, 'Maintenance Supply', 'Janitor Closet'),
                ('Mop', 'piece', 'String mop', 225.00, 'Maintenance Supply', 'Janitor Closet'),
                ('Dustpan', 'piece', 'Plastic dustpan', 95.00, 'Maintenance Supply', 'Janitor Closet'),
                ('Rubber Gloves', 'pair', 'Cleaning rubber gloves', 45.00, 'Hygiene Plus', 'Janitor Closet'),
                ('Sponge', 'piece', 'Kitchen sponge', 25.00, 'Clean Pro', 'Janitor Closet'),
                ('Scrub Brush', 'piece', 'Cleaning scrub brush', 65.00, 'Clean Pro', 'Janitor Closet'),
                ('Air Freshener', 'bottle', 'Room air freshener', 125.00, 'Hygiene Plus', 'Janitor Closet'),
            ],
            'Medical Supplies': [
                ('First Aid Kit', 'set', 'Complete first aid kit', 850.00, 'Medical Supply Corp', 'Medical Storage'),
                ('Bandages', 'box', 'Adhesive bandages', 125.00, 'Health Plus', 'Medical Storage'),
                ('Gauze Pads', 'pack', 'Sterile gauze pads', 85.00, 'Medical Supply Corp', 'Medical Storage'),
                ('Medical Tape', 'roll', 'Medical adhesive tape', 65.00, 'Health Plus', 'Medical Storage'),
                ('Antiseptic', 'bottle', 'Antiseptic solution', 95.00, 'Pharmacy Direct', 'Medical Storage'),
                ('Thermometer', 'piece', 'Digital thermometer', 450.00, 'Medical Equipment Inc', 'Medical Storage'),
                ('Disposable Gloves', 'box', 'Latex disposable gloves', 185.00, 'Health Plus', 'Medical Storage'),
                ('Face Masks', 'box', 'Surgical face masks', 225.00, 'Medical Supply Corp', 'Medical Storage'),
                ('Hand Sanitizer', 'bottle', 'Alcohol-based sanitizer', 125.00, 'Pharmacy Direct', 'Medical Storage'),
            ],
            'Stationery': [
                ('Marker Permanent Black', 'piece', 'Black permanent marker', 35.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Marker Permanent Red', 'piece', 'Red permanent marker', 35.00, 'Office Warehouse', 'Supply Cabinet 1'),
                ('Highlighter Yellow', 'piece', 'Yellow highlighter', 45.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Highlighter Green', 'piece', 'Green highlighter', 45.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Highlighter Pink', 'piece', 'Pink highlighter', 45.00, 'School Supplies Inc', 'Supply Cabinet 1'),
                ('Binder Clips Large', 'box', 'Large binder clips', 85.00, 'Office Depot', 'Supply Cabinet 2'),
                ('Binder Clips Small', 'box', 'Small binder clips', 65.00, 'Office Depot', 'Supply Cabinet 2'),
                ('Push Pins', 'box', 'Colored push pins', 45.00, 'Office Warehouse', 'Supply Cabinet 2'),
                ('Index Cards', 'pack', 'Ruled index cards', 65.00, 'National Book Store', 'Supply Cabinet 2'),
                ('Sticky Notes', 'pack', 'Yellow sticky notes', 85.00, 'Office Depot', 'Supply Cabinet 1'),
                ('Notebook', 'piece', 'Spiral notebook 100 pages', 45.00, 'School Supplies Inc', 'Supply Cabinet 2'),
                ('Legal Pad', 'piece', 'Yellow legal pad', 65.00, 'National Book Store', 'Supply Cabinet 2'),
            ],
            'Furniture': [
                ('Office Chair', 'piece', 'Ergonomic office chair', 4500.00, 'Office Furniture Plus', 'Furniture Storage'),
                ('Office Table', 'piece', 'Standard office desk', 6500.00, 'Office Furniture Plus', 'Furniture Storage'),
                ('Filing Cabinet', 'piece', '4-drawer filing cabinet', 8500.00, 'Steel Furniture Co', 'Furniture Storage'),
                ('Bookshelf', 'piece', '5-tier bookshelf', 3500.00, 'Wood Craft Inc', 'Furniture Storage'),
                ('Whiteboard', 'piece', '4x6 feet whiteboard', 2850.00, 'School Equipment Co', 'Conference Room'),
                ('Cork Board', 'piece', '3x4 feet cork board', 1250.00, 'Office Depot', 'Conference Room'),
                ('Electric Fan', 'piece', 'Ceiling electric fan', 2500.00, 'Appliance Center', 'Equipment Storage'),
                ('Water Dispenser', 'piece', 'Hot and cold dispenser', 8500.00, 'Appliance Center', 'Equipment Storage'),
                ('Microwave Oven', 'piece', 'Countertop microwave', 4500.00, 'Appliance Center', 'Equipment Storage'),
                ('Refrigerator', 'piece', 'Small office refrigerator', 12500.00, 'Appliance Center', 'Equipment Storage'),
            ]
        }

        update_existing = options.get('update_existing', False)
        created_count = 0
        updated_count = 0

        for category_name, items in supply_categories.items():
            # Get the category instance (if it exists)
            category_instance = categories.get(category_name)

            for item_data in items:
                name, unit, description, unit_cost, supplier, location = item_data

                # Check if item already exists
                existing_item = SupplyItem.objects.filter(name=name).first()

                if existing_item:
                    if update_existing:
                        # Update existing item with new fields
                        existing_item.category = category_instance
                        existing_item.unit_cost = unit_cost
                        existing_item.supplier = supplier
                        existing_item.location = location
                        existing_item.description = description
                        existing_item.save()
                        updated_count += 1
                    continue

                # Create the supply item with enhanced fields
                SupplyItem.objects.create(
                    name=name,
                    description=description,
                    category=category_instance,  # Assign category (can be None if not found)
                    unit=unit,
                    current_stock=random.randint(10, 100),
                    minimum_stock=random.randint(5, 20),
                    unit_cost=unit_cost,
                    supplier=supplier,
                    location=location
                )
                created_count += 1

        if created_count > 0:
            self.stdout.write(f'Created {created_count} new supply items with categories and enhanced fields')
        if updated_count > 0:
            self.stdout.write(f'Updated {updated_count} existing items with categories and enhanced fields')
        if created_count == 0 and updated_count == 0:
            self.stdout.write('No items were created or updated (all items already exist)')

    def create_gso_account(self):
        """Create new GSO account"""
        try:
            # Create or get GSO user
            gso_user, created = User.objects.get_or_create(
                username='gso_admin',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'GSO',
                    'last_name': 'Administrator'
                }
            )

            if created:
                gso_user.set_password('gso123')
                gso_user.save()
                self.stdout.write('Created GSO admin user')
            else:
                self.stdout.write('GSO admin user already exists')

            # Create or get GSO profile
            profile, profile_created = UserProfile.objects.get_or_create(
                user=gso_user,
                defaults={
                    'role': 'GSO',
                    'department': 'General Services Office',
                    'phone': '***********'
                }
            )

            if profile_created:
                self.stdout.write('Created GSO admin profile')
            else:
                # Update existing profile
                profile.role = 'GSO'
                profile.department = 'General Services Office'
                profile.save()
                self.stdout.write('Updated GSO admin profile')

            self.stdout.write('GSO Account Details:')
            self.stdout.write('Username: gso_admin')
            self.stdout.write('Password: gso123')
        except Exception as e:
            self.stdout.write(f'Error creating GSO account: {str(e)}')
