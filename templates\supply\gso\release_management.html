{% extends 'base_new.html' %}

{% block title %}Release Management - MSRRMS{% endblock %}

{% block page_title %}Release Management{% endblock %}
{% block mobile_title %}Release Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="releaseManagement()">
    <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-8 text-white mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold">Release Management</h1>
                    <p class="text-blue-100 mt-2 text-lg">Manage approved requests ready for release</p>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold">{{ total_approved }}</div>
                        <div class="text-blue-200 text-sm">Ready for Release</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-200">
                            {% with available_count=0 %}
                                {% for request in page_obj %}
                                    {% if request.can_be_released %}
                                        {% with available_count=available_count|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                {{ available_count }}
                            {% endwith %}
                        </div>
                        <div class="text-blue-200 text-sm">Stock Available</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-yellow-200">
                            {% with pending_count=0 %}
                                {% for request in page_obj %}
                                    {% if not request.can_be_released %}
                                        {% with pending_count=pending_count|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                {{ pending_count }}
                            {% endwith %}
                        </div>
                        <div class="text-blue-200 text-sm">Insufficient Stock</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Cards (Mobile) -->
        <div class="grid grid-cols-2 md:hidden gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ total_approved }}</div>
                <div class="text-sm text-gray-600">Ready for Release</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-green-600">
                    {% with available_count=0 %}
                        {% for request in page_obj %}
                            {% if request.can_be_released %}
                                {% with available_count=available_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ available_count }}
                    {% endwith %}
                </div>
                <div class="text-sm text-gray-600">Stock Available</div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-5">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Filter & Search</h3>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                    </svg>
                </div>

                <form hx-get="{% url 'supply:release_management' %}"
                      hx-target="#release-list"
                      hx-swap="outerHTML"
                      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

                    <!-- Department Filter -->
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Department
                        </label>
                        <select name="department" id="department"
                                class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                            <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </label>
                        <input type="text" name="search" id="search" value="{{ search_query }}"
                               placeholder="Request ID, item, or requester..."
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                    </div>

                    <!-- Sort -->
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
                            </svg>
                            Sort By
                        </label>
                        <select name="sort" id="sort"
                                class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                            <option value="-approved_at" {% if sort_by == '-approved_at' %}selected{% endif %}>Newest Approved</option>
                            <option value="approved_at" {% if sort_by == 'approved_at' %}selected{% endif %}>Oldest Approved</option>
                            <option value="department" {% if sort_by == 'department' %}selected{% endif %}>Department A-Z</option>
                            <option value="-department" {% if sort_by == '-department' %}selected{% endif %}>Department Z-A</option>
                            <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item A-Z</option>
                            <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Z-A</option>
                        </select>
                    </div>

                    <!-- Filter Button -->
                    <div class="flex items-end">
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-2.5 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                            </svg>
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Batch Operations -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Batch Operations</h3>
                            <p class="text-sm text-gray-500">Select multiple requests to release them together</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button @click="showBulkReleaseModal = true"
                                :disabled="selectedRequests.length === 0"
                                :class="selectedRequests.length === 0 ? 'bg-gray-300 cursor-not-allowed text-gray-500' : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Release Selected (<span x-text="selectedRequests.length"></span>)
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests List -->
        <div id="release-list">
            {% include 'supply/gso/release_list.html' %}
        </div>

        <!-- Bulk Release Modal -->
        <div x-show="showBulkReleaseModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showBulkReleaseModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <form hx-post="{% url 'supply:batch_operations' %}"
                      hx-target="#release-list"
                      hx-swap="outerHTML">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_release">
                    <template x-for="requestId in selectedRequests">
                        <input type="hidden" name="selected_requests" :value="requestId">
                    </template>
                    
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 text-center">Bulk Release</h3>
                        <div class="mt-4">
                            <label for="release_notes" class="block text-sm font-medium text-gray-700">Release Notes (Optional)</label>
                            <textarea name="release_notes" 
                                      id="release_notes"
                                      rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                                      placeholder="Add notes for this bulk release..."></textarea>
                        </div>
                        <div class="flex justify-center space-x-4 px-4 py-3 mt-4">
                            <button type="submit"
                                    @click="showBulkReleaseModal = false; selectedRequests = []"
                                    class="px-4 py-2 bg-purple-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">
                                Release All
                            </button>
                            <button type="button"
                                    @click="showBulkReleaseModal = false" 
                                    class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function releaseManagement() {
    return {
        selectedRequests: [],
        showBulkReleaseModal: false,
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll() {
            const checkboxes = document.querySelectorAll('input[name="request_checkbox"]');
            if (this.selectedRequests.length === checkboxes.length) {
                this.selectedRequests = [];
            } else {
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            }
        }
    }
}
</script>
{% endblock %}
