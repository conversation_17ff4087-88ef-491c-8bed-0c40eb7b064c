<!-- Items Grid Partial Template for HTMX Updates -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {% for item in items %}
    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                        </svg>
                    </div>
                    <h4 class="font-medium text-gray-900">{{ item.name }}</h4>
                </div>
                <p class="text-sm text-gray-600 mb-2">{{ item.description }}</p>
                <div class="flex items-center justify-between text-xs">
                    <span class="text-gray-500">{{ item.unit }}</span>
                    <span class="{% if item.is_low_stock %}text-red-600{% else %}text-green-600{% endif %} font-medium">
                        {{ item.current_stock }} available
                    </span>
                </div>
                
                <!-- Recently Requested Indicator -->
                {% if item.id in recent_items %}
                <div class="mt-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Recently Requested
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-3 pt-3 border-t border-gray-200">
            {% if item.current_stock > 0 %}
                <button @click="addToCart({{ item.id }}, '{{ item.name|escapejs }}', '{{ item.description|escapejs }}', {{ item.current_stock }}, '{{ item.unit|escapejs }}')"
                        :disabled="isInCart({{ item.id }})"
                        :class="isInCart({{ item.id }}) ? 'bg-gray-300 cursor-not-allowed text-gray-600' : 'bg-blue-600 hover:bg-blue-700 text-white'"
                        class="w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <span x-show="!isInCart({{ item.id }})">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add to Cart
                    </span>
                    <span x-show="isInCart({{ item.id }})">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Added to Cart
                    </span>
                </button>
            {% else %}
                <button disabled class="w-full px-3 py-2 text-sm font-medium text-gray-500 bg-red-100 rounded-lg cursor-not-allowed flex items-center justify-center">
                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                    </svg>
                    Out of Stock
                </button>
            {% endif %}
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <div class="mx-auto h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No items found</h3>
        <p class="text-gray-500 mb-6">Try adjusting your search or filter criteria.</p>
        
        <!-- Quick Filter Suggestions -->
        <div class="flex flex-wrap justify-center gap-2">
            <button hx-get="{% url 'supply:search_items' %}?category=Office+Supplies&in_stock_only=on" 
                    hx-target="#items-grid"
                    class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors duration-200">
                Office Supplies
            </button>
            <button hx-get="{% url 'supply:search_items' %}?category=Computer+Supplies&in_stock_only=on" 
                    hx-target="#items-grid"
                    class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors duration-200">
                Computer Supplies
            </button>
            <button hx-get="{% url 'supply:search_items' %}?category=Cleaning+Supplies&in_stock_only=on" 
                    hx-target="#items-grid"
                    class="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors duration-200">
                Cleaning Supplies
            </button>
            <button hx-get="{% url 'supply:search_items' %}?search=&category=&in_stock_only=on" 
                    hx-target="#items-grid"
                    class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors duration-200">
                Show All
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Loading Indicator -->
<div class="htmx-indicator flex items-center justify-center py-8">
    <div class="flex items-center text-blue-600">
        <svg class="animate-spin h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm font-medium">Loading items...</span>
    </div>
</div>
