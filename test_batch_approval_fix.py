#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest
from django.contrib.auth.models import User
from django.utils import timezone

def test_batch_approval_fix():
    print("=== Testing Batch Approval Fix ===")
    
    # Get a pending batch request
    batch_request = SupplyRequest.objects.filter(is_batch_request=True, status='PENDING').first()
    
    if not batch_request:
        print("No pending batch requests found")
        return
    
    print(f"\nTesting with request: {batch_request.request_id}")
    print(f"Current status: {batch_request.status}")
    print(f"Items count: {batch_request.request_items.count()}")
    
    # Get GSO user
    gso_user = User.objects.filter(userprofile__role='GSO').first()
    if not gso_user:
        print("No GSO user found")
        return
    
    print(f"GSO user: {gso_user.username}")
    
    # Test the approval logic manually (simulating the view logic)
    print(f"\n=== Simulating Approval Process ===")
    
    print("Before approval:")
    print(f"  Status: {batch_request.status}")
    print(f"  Approved by: {batch_request.approved_by}")
    print(f"  Approved at: {batch_request.approved_at}")
    print(f"  Approval remarks: {batch_request.approval_remarks}")
    
    # Simulate approve_all action (same logic as in the view)
    remarks = "Test approval - fixing log_request_action error"
    
    try:
        # Apply the same logic as in the view
        batch_request.status = 'APPROVED'
        batch_request.approved_by = gso_user
        batch_request.approved_at = timezone.now()
        batch_request.approval_remarks = remarks
        batch_request.save()
        
        # Update all items in the batch
        for request_item in batch_request.request_items.all():
            request_item.approved_quantity = request_item.quantity
            request_item.remarks = remarks
            request_item.save()
        
        print("\n✓ Approval logic executed successfully!")
        
        print("\nAfter approval:")
        print(f"  Status: {batch_request.status}")
        print(f"  Approved by: {batch_request.approved_by.username if batch_request.approved_by else None}")
        print(f"  Approved at: {batch_request.approved_at}")
        print(f"  Approval remarks: {batch_request.approval_remarks}")
        
        print("\nItem approval status:")
        for request_item in batch_request.request_items.all():
            print(f"  - {request_item.item.name}: {request_item.approved_quantity}/{request_item.quantity}")
        
        # Test the audit logging (this was the source of the error)
        print(f"\n=== Testing Audit Logging ===")
        from supply.audit import log_request_action
        
        # Create a mock request object
        class MockRequest:
            def __init__(self, user):
                self.user = user
        
        mock_request = MockRequest(gso_user)
        
        # Test the corrected log_request_action call
        log_request_action(
            user=gso_user,
            action_type='APPROVED',
            supply_request=batch_request,
            request=None,  # We can pass None for testing
            additional_data={'approval_remarks': remarks}
        )
        
        print("✓ Audit logging works correctly!")
        
        print(f"\n=== Test Complete ===")
        print("The batch request approval functionality should now work without errors.")
        print(f"You can test it in the browser at: http://127.0.0.1:8000/gso/batch-requests/{batch_request.id}/")
        
    except Exception as e:
        print(f"✗ Error during approval: {e}")
        import traceback
        traceback.print_exc()
    
    # Reset for further testing
    reset = input("\nReset request to PENDING for further testing? (y/n): ").lower().strip()
    if reset == 'y':
        batch_request.status = 'PENDING'
        batch_request.approved_by = None
        batch_request.approved_at = None
        batch_request.approval_remarks = ''
        batch_request.save()
        
        for request_item in batch_request.request_items.all():
            request_item.approved_quantity = None
            request_item.remarks = ''
            request_item.save()
        
        print("✓ Request reset to PENDING status")

if __name__ == '__main__':
    test_batch_approval_fix()
