from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from .models import UserProfile, SupplyItem, SupplyRequest
import re


class AccessibilityTestCase(TestCase):
    """Accessibility tests for WCAG compliance"""
    
    def setUp(self):
        """Set up test data"""
        # Create users
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for accessibility testing',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()

    def test_semantic_html_structure(self):
        """Test that pages use proper semantic HTML elements"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Test main pages
        pages = [
            reverse('supply:dashboard'),
            reverse('supply:request_create'),
            reverse('supply:request_history'),
        ]
        
        for page_url in pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 200)
            content = response.content.decode('utf-8')
            
            # Check for semantic elements
            self.assertIn('<main', content, f"Page {page_url} should have a main element")
            self.assertIn('<nav', content, f"Page {page_url} should have navigation")
            self.assertIn('<header', content, f"Page {page_url} should have a header")

    def test_form_labels_and_accessibility(self):
        """Test that forms have proper labels and accessibility attributes"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for form labels
        self.assertIn('<label', content, "Forms should have labels")
        
        # Check that inputs have associated labels or aria-labels
        input_pattern = r'<input[^>]*>'
        inputs = re.findall(input_pattern, content)
        
        for input_tag in inputs:
            # Skip hidden inputs and CSRF tokens
            if 'type="hidden"' in input_tag or 'csrfmiddlewaretoken' in input_tag:
                continue
            
            # Should have either id that matches a label, or aria-label
            has_id = 'id=' in input_tag
            has_aria_label = 'aria-label=' in input_tag
            
            self.assertTrue(
                has_id or has_aria_label,
                f"Input should have id or aria-label: {input_tag}"
            )

    def test_button_accessibility(self):
        """Test that buttons have proper accessibility attributes"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for buttons
        button_pattern = r'<button[^>]*>(.*?)</button>'
        buttons = re.findall(button_pattern, content, re.DOTALL)
        
        for button_content in buttons:
            # Buttons should have text content or aria-label
            text_content = re.sub(r'<[^>]+>', '', button_content).strip()
            self.assertTrue(
                len(text_content) > 0,
                f"Button should have text content: {button_content}"
            )

    def test_image_alt_attributes(self):
        """Test that images have alt attributes"""
        self.client.login(username='dept_user', password='testpass123')
        
        pages = [
            reverse('supply:dashboard'),
            reverse('supply:request_create'),
        ]
        
        for page_url in pages:
            response = self.client.get(page_url)
            self.assertEqual(response.status_code, 200)
            content = response.content.decode('utf-8')
            
            # Find all img tags
            img_pattern = r'<img[^>]*>'
            images = re.findall(img_pattern, content)
            
            for img_tag in images:
                # Each image should have an alt attribute
                self.assertIn('alt=', img_tag, f"Image should have alt attribute: {img_tag}")

    def test_heading_hierarchy(self):
        """Test proper heading hierarchy (h1, h2, h3, etc.)"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for h1 tag
        self.assertIn('<h1', content, "Page should have an h1 heading")
        
        # Extract all headings
        heading_pattern = r'<h([1-6])[^>]*>'
        headings = re.findall(heading_pattern, content)
        
        if headings:
            # First heading should be h1
            first_heading_level = int(headings[0])
            self.assertEqual(first_heading_level, 1, "First heading should be h1")

    def test_color_contrast_indicators(self):
        """Test that color is not the only way to convey information"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Create a request to test status indicators
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=10,
            purpose='Accessibility test request',
            status='PENDING'
        )
        
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Status indicators should have text, not just color
        # Look for status badges/spans
        if 'Pending' in content:
            # Should have text content, not just background color
            self.assertIn('Pending', content, "Status should be indicated by text, not just color")

    def test_keyboard_navigation_support(self):
        """Test that interactive elements support keyboard navigation"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for tabindex attributes on interactive elements
        # Links and buttons should be keyboard accessible
        link_pattern = r'<a[^>]*href[^>]*>'
        links = re.findall(link_pattern, content)
        
        # Links should not have negative tabindex (unless intentionally hidden)
        for link in links:
            if 'tabindex=' in link:
                tabindex_match = re.search(r'tabindex="(-?\d+)"', link)
                if tabindex_match:
                    tabindex = int(tabindex_match.group(1))
                    # Negative tabindex should only be used intentionally
                    if tabindex < 0:
                        # Should have a good reason (like being in a dropdown)
                        pass

    def test_aria_attributes(self):
        """Test proper use of ARIA attributes"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for ARIA landmarks
        aria_patterns = [
            r'role="main"',
            r'role="navigation"',
            r'aria-label=',
            r'aria-hidden=',
        ]
        
        # At least some ARIA attributes should be present
        aria_found = False
        for pattern in aria_patterns:
            if re.search(pattern, content):
                aria_found = True
                break
        
        # Note: This is a basic check. In a real application, you'd want more comprehensive ARIA testing

    def test_form_error_accessibility(self):
        """Test that form errors are accessible"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Submit invalid form to trigger errors
        response = self.client.post(reverse('supply:request_create'), {
            'item': '',  # Missing required field
            'quantity': '',  # Missing required field
            'purpose': ''  # Missing required field
        })
        
        # Should return form with errors
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Error messages should be associated with form fields
        if 'error' in content.lower():
            # Errors should be clearly marked
            self.assertTrue(
                'text-red' in content or 'error' in content.lower(),
                "Form errors should be clearly indicated"
            )

    def test_skip_navigation_links(self):
        """Test for skip navigation links for screen readers"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Look for skip links (usually hidden but accessible to screen readers)
        skip_patterns = [
            r'skip.*main',
            r'skip.*content',
            r'sr-only',  # Screen reader only content
        ]
        
        skip_found = False
        for pattern in skip_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                skip_found = True
                break
        
        # Note: Skip links are recommended but not always required

    def test_table_accessibility(self):
        """Test that data tables have proper headers and structure"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for table headers
        if '<table' in content:
            self.assertIn('<th', content, "Tables should have header cells")
            self.assertIn('<thead', content, "Tables should have thead element")

    def test_focus_management(self):
        """Test that focus is properly managed"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check that there are no elements with outline: none without alternative focus indicators
        # This is a basic check - in practice, you'd test actual focus behavior
        
        # Interactive elements should be focusable
        interactive_elements = ['button', 'input', 'select', 'textarea', 'a']
        
        for element in interactive_elements:
            if f'<{element}' in content:
                # Element exists, should be focusable
                # This is a placeholder for more comprehensive focus testing
                pass

    def test_responsive_design_accessibility(self):
        """Test that responsive design maintains accessibility"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for viewport meta tag
        self.assertIn('viewport', content, "Page should have viewport meta tag for mobile accessibility")
        
        # Check for responsive classes (Tailwind CSS)
        responsive_classes = ['sm:', 'md:', 'lg:', 'xl:']
        responsive_found = False
        
        for responsive_class in responsive_classes:
            if responsive_class in content:
                responsive_found = True
                break
        
        self.assertTrue(responsive_found, "Page should use responsive design classes")

    def test_language_attributes(self):
        """Test that language attributes are properly set"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Check for lang attribute on html element
        self.assertIn('lang=', content, "HTML element should have lang attribute")
