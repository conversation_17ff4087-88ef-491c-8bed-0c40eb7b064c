from django.core.management.base import BaseCommand
from django.db import transaction
from supply.models import SupplyRequest, SupplyRequestItem
from django.utils import timezone


class Command(BaseCommand):
    help = '''Fix data integrity issues in the supply request system:
    - Remove batch requests that have no items
    - Fix auto-approved requests that are missing approval metadata
    - Clean up orphaned requests
    '''

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )
        parser.add_argument(
            '--fix-empty-batch',
            action='store_true',
            help='Fix empty batch requests',
        )
        parser.add_argument(
            '--fix-auto-approved',
            action='store_true',
            help='Fix auto-approved requests missing approval metadata',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        fix_empty_batch = options['fix_empty_batch']
        fix_auto_approved = options['fix_auto_approved']
        
        if not any([fix_empty_batch, fix_auto_approved]):
            # If no specific fix is requested, do all
            fix_empty_batch = True
            fix_auto_approved = True

        self.stdout.write('=== Supply Request Data Integrity Check ===')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        total_fixed = 0
        
        if fix_empty_batch:
            total_fixed += self.fix_empty_batch_requests(dry_run)
        
        if fix_auto_approved:
            total_fixed += self.fix_auto_approved_requests(dry_run)
        
        if total_fixed > 0:
            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'Found {total_fixed} issues that would be fixed')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully fixed {total_fixed} issues')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('No data integrity issues found')
            )

    def fix_empty_batch_requests(self, dry_run=False):
        """Fix batch requests that have no items"""
        self.stdout.write('\n--- Checking for empty batch requests ---')

        # Find batch requests with no items (using count instead of isnull)
        from django.db.models import Count
        empty_batch_requests = SupplyRequest.objects.filter(
            is_batch_request=True
        ).annotate(
            item_count=Count('request_items')
        ).filter(
            item_count=0
        )

        count = empty_batch_requests.count()
        self.stdout.write(f'Found {count} empty batch requests')

        if count > 0:
            for request in empty_batch_requests:
                self.stdout.write(
                    f'  - {request.request_id}: Created by {request.requester.username} '
                    f'on {request.created_at.strftime("%Y-%m-%d %H:%M")}, Status: {request.status}'
                )

                if not dry_run:
                    # Delete the empty batch request
                    request.delete()
                    self.stdout.write(f'    → Deleted empty batch request {request.request_id}')

        # Also check for requests with missing items (item field is null)
        self.stdout.write('\n--- Checking for requests with missing items ---')
        requests_with_missing_items = SupplyRequest.objects.filter(
            item__isnull=True,
            is_batch_request=False  # Single requests should have an item
        )

        missing_count = requests_with_missing_items.count()
        self.stdout.write(f'Found {missing_count} single requests with missing items')

        if missing_count > 0:
            for request in requests_with_missing_items:
                self.stdout.write(
                    f'  - {request.request_id}: Created by {request.requester.username} '
                    f'on {request.created_at.strftime("%Y-%m-%d %H:%M")}, Status: {request.status}'
                )

                if not dry_run:
                    # Delete the request with missing item
                    request.delete()
                    self.stdout.write(f'    → Deleted request with missing item {request.request_id}')

        return count + missing_count

    def fix_auto_approved_requests(self, dry_run=False):
        """Fix requests that are approved but missing approval metadata"""
        self.stdout.write('\n--- Checking for auto-approved requests ---')
        
        # Find approved requests without proper approval metadata
        auto_approved_requests = SupplyRequest.objects.filter(
            status='APPROVED',
            approved_by__isnull=True
        )
        
        count = auto_approved_requests.count()
        self.stdout.write(f'Found {count} auto-approved requests without proper metadata')
        
        if count > 0:
            for request in auto_approved_requests:
                self.stdout.write(
                    f'  - {request.request_id}: Status=APPROVED but no approver set'
                )
                
                if not dry_run:
                    # Reset to PENDING status so it requires proper approval
                    request.status = 'PENDING'
                    request.approved_by = None
                    request.approved_at = None
                    request.approval_remarks = ''
                    request.save()
                    self.stdout.write(f'    → Reset {request.request_id} to PENDING status')
        
        return count

    def check_data_consistency(self):
        """Additional data consistency checks"""
        self.stdout.write('\n--- Additional consistency checks ---')
        
        # Check for requests with items but marked as non-batch
        single_requests_with_items = SupplyRequest.objects.filter(
            is_batch_request=False,
            request_items__isnull=False
        ).distinct()
        
        if single_requests_with_items.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'Found {single_requests_with_items.count()} single requests '
                    'that have batch items - this may indicate data inconsistency'
                )
            )
        
        # Check for batch requests with both legacy item field and batch items
        mixed_requests = SupplyRequest.objects.filter(
            is_batch_request=True,
            item__isnull=False,
            request_items__isnull=False
        ).distinct()
        
        if mixed_requests.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'Found {mixed_requests.count()} batch requests '
                    'with both legacy item and batch items'
                )
            )
