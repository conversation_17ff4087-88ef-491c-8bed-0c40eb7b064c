{% extends 'base_new.html' %}

{% block title %}Create Supply Request - MSRRMS{% endblock %}

{% block page_title %}New Request{% endblock %}
{% block mobile_title %}New Request{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
            </div>
            <div>
                <h1 class="text-2xl font-bold">Create Supply Request</h1>
                <p class="text-blue-100 mt-1">Submit a new request for supplies and materials</p>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Request Details</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-600">2</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Review & Submit</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100" x-data="requestForm()">
        <div class="px-6 py-5">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Request Information</h3>

            <form method="post"
                  hx-post="{% url 'supply:request_create' %}"
                  hx-target="#form-container"
                  hx-swap="outerHTML"
                  hx-indicator="#form-loading"
                  @submit="handleSubmit">
                <div id="form-container">
                    {% csrf_token %}

                    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <!-- Supply Item Field -->
                        <div class="space-y-2">
                            <label for="{{ form.item.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                </svg>
                                Supply Item <span class="text-red-500">*</span>
                            </label>
                            <select name="item"
                                    id="{{ form.item.id_for_label }}"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm"
                                    @change="updateItemInfo">
                                <option value="">Select an item...</option>
                                {% for choice in form.item.field.choices %}
                                    {% if choice.0 %}
                                        <option value="{{ choice.0 }}" {% if choice.0 == form.item.value %}selected{% endif %}>{{ choice.1 }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                            {% if form.item.errors %}
                                <div class="mt-1 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {{ form.item.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Quantity Field -->
                        <div class="space-y-2">
                            <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                </svg>
                                Quantity <span class="text-red-500">*</span>
                            </label>
                            <input type="number"
                                   name="quantity"
                                   id="{{ form.quantity.id_for_label }}"
                                   value="{{ form.quantity.value|default:'' }}"
                                   min="1"
                                   placeholder="Enter quantity"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm"
                                   @input="validateQuantity">
                            {% if form.quantity.errors %}
                                <div class="mt-1 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {{ form.quantity.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Item Information Display -->
                    <div class="mt-6">
                        <div id="item-info" class="transition-all duration-300">
                            <!-- Will be populated by HTMX -->
                        </div>
                    </div>

                    <!-- Purpose Field -->
                    <div class="mt-6 space-y-2">
                        <label for="{{ form.purpose.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Purpose <span class="text-red-500">*</span>
                        </label>
                        <textarea name="purpose"
                                  id="{{ form.purpose.id_for_label }}"
                                  rows="4"
                                  placeholder="Describe why you need these supplies..."
                                  class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">{{ form.purpose.value|default:'' }}</textarea>
                        <p class="text-sm text-gray-500 flex items-center">
                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Describe why you need these supplies (minimum 10 characters)
                        </p>
                        {% if form.purpose.errors %}
                            <div class="mt-1 text-sm text-red-600 flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {{ form.purpose.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Request Summary -->
                    {% if user.userprofile.department %}
                    <div class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Request Summary
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Department</p>
                                    <p class="text-sm text-gray-600">{{ user.userprofile.department }}</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Requested by</p>
                                    <p class="text-sm text-gray-600">{{ user.get_full_name|default:user.username }}</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Request Date</p>
                                    <p class="text-sm text-gray-600">{{ "now"|date:"F d, Y" }}</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Status</p>
                                    <p class="text-sm text-yellow-600 font-medium">Pending Submission</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Form Actions -->
                    <div class="flex flex-col sm:flex-row justify-end mt-8 space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                        <a href="{% url 'supply:dashboard' %}"
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            Cancel
                        </a>
                        <button type="submit"
                                :disabled="isSubmitting"
                                :class="isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                                class="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <svg x-show="isSubmitting" class="animate-spin h-5 w-5 mr-2 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <svg x-show="!isSubmitting" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            <span x-text="isSubmitting ? 'Submitting Request...' : 'Submit Request'"></span>
                        </button>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="form-loading" class="htmx-indicator flex items-center justify-center py-4">
                        <div class="flex items-center text-blue-600">
                            <svg class="animate-spin h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="text-sm font-medium">Processing your request...</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function requestForm() {
    return {
        selectedItem: '',
        isSubmitting: false,

        updateItemInfo() {
            // This function is now simpler as HTMX handles showing/hiding content
            this.selectedItem = document.getElementById('{{ form.item.id_for_label }}').value;
        },

        handleSubmit() {
            this.isSubmitting = true;
            // The notification can be triggered via HTMX events for better consistency
        },

        validateQuantity(event) {
            const value = parseInt(event.target.value);
            const selectedOption = document.querySelector(`#{{ form.item.id_for_label }} option:checked`);

            if (selectedOption && selectedOption.value) {
                const availableStock = parseInt(selectedOption.textContent.match(/\((\d+)/)?.[1] || 0);
                if (value > availableStock) {
                    showNotification('warning', 'Quantity Warning', `Requested quantity (${value}) exceeds available stock (${availableStock})`);
                }
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const itemSelect = document.getElementById('{{ form.item.id_for_label }}');
    
    if (itemSelect) {
        itemSelect.setAttribute('hx-get', '{% url "supply:item_info" %}');
        itemSelect.setAttribute('hx-target', '#item-info');
        itemSelect.setAttribute('hx-trigger', 'change');
        htmx.process(itemSelect);
    }

    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        const form = evt.target.closest('form');
        if (form) {
            showNotification('info', 'Processing', 'Submitting your request...');
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful) {
            const form = event.target.closest('form');
            if (form && event.detail.target.id === 'form-container') {
                showNotification('success', 'Request Submitted', 'Your supply request has been submitted successfully.');
            }
        } else if (event.detail.failed) {
            showNotification('error', 'Submission Error', 'There was an error submitting your request. Please check the form and try again.');
        }
    });
});
</script>
{% endblock %}