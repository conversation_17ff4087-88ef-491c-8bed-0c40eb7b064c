from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.test import override_settings
from .models import UserProfile, SupplyItem, SupplyRequest
import json


class SecurityTestCase(TestCase):
    """Security tests for vulnerability assessment"""
    
    def setUp(self):
        """Set up test data"""
        # Create users
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create malicious user
        self.malicious_user = User.objects.create_user(
            username='malicious_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.malicious_profile = UserProfile.objects.get(user=self.malicious_user)
        self.malicious_profile.role = 'DEPARTMENT'
        self.malicious_profile.department = 'Other Department'
        self.malicious_profile.save()
        
        # Create supply item
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for security testing',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        # Create test request
        self.test_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=10,
            purpose='Security test request',
            status='PENDING'
        )
        
        self.client = Client()

    def test_authentication_required(self):
        """Test that authentication is required for protected views"""
        protected_urls = [
            reverse('supply:dashboard'),
            reverse('supply:request_create'),
            reverse('supply:request_history'),
            reverse('supply:gso_dashboard'),
            reverse('supply:inventory'),
            reverse('supply:reports'),
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            # Should redirect to login
            self.assertEqual(response.status_code, 302)
            self.assertIn('login', response.url.lower())

    def test_role_based_access_control(self):
        """Test that role-based access control is enforced"""
        # Department user should not access GSO functions
        self.client.login(username='dept_user', password='testpass123')
        
        gso_only_urls = [
            reverse('supply:gso_dashboard'),
            reverse('supply:inventory'),
            reverse('supply:reports'),
            reverse('supply:approve_request', args=[self.test_request.id]),
            reverse('supply:release_request', args=[self.test_request.id]),
        ]
        
        for url in gso_only_urls:
            response = self.client.get(url)
            # Should be redirected or forbidden
            self.assertIn(response.status_code, [302, 403])

    def test_object_level_permissions(self):
        """Test that users can only access their own objects"""
        # Create request for dept_user
        dept_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=5,
            purpose='Department user request',
            status='PENDING'
        )
        
        # Malicious user tries to access other user's request
        self.client.login(username='malicious_user', password='testpass123')
        
        response = self.client.get(reverse('supply:request_detail', args=[dept_request.id]))
        # Should be forbidden or redirected
        self.assertIn(response.status_code, [302, 403, 404])

    def test_csrf_protection(self):
        """Test CSRF protection on forms"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Try to submit form without CSRF token
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 10,
            'purpose': 'CSRF test request'
        }, HTTP_X_CSRFTOKEN='invalid_token')
        
        # Should be rejected due to CSRF
        self.assertEqual(response.status_code, 403)

    def test_sql_injection_protection(self):
        """Test protection against SQL injection"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Try SQL injection in search
        malicious_queries = [
            "'; DROP TABLE supply_supplyrequest; --",
            "' OR '1'='1",
            "1' UNION SELECT * FROM auth_user --",
        ]
        
        for query in malicious_queries:
            response = self.client.get(reverse('supply:global_search'), {
                'q': query
            })
            # Should handle gracefully without error
            self.assertEqual(response.status_code, 200)
            
            # Should not return sensitive data
            content = response.content.decode('utf-8')
            self.assertNotIn('password', content.lower())
            self.assertNotIn('secret', content.lower())

    def test_xss_protection(self):
        """Test protection against Cross-Site Scripting (XSS)"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Try XSS in form fields
        xss_payloads = [
            '<script>alert("XSS")</script>',
            '"><script>alert("XSS")</script>',
            'javascript:alert("XSS")',
            '<img src=x onerror=alert("XSS")>',
        ]
        
        for payload in xss_payloads:
            response = self.client.post(reverse('supply:request_create'), {
                'item': self.supply_item.id,
                'quantity': 10,
                'purpose': payload
            })
            
            # Form should either reject or escape the payload
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                # Script tags should be escaped
                self.assertNotIn('<script>', content)
                self.assertNotIn('javascript:', content)

    def test_directory_traversal_protection(self):
        """Test protection against directory traversal attacks"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Try directory traversal in file-related operations
        traversal_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2f',
        ]
        
        for payload in traversal_payloads:
            # Test in export functionality
            response = self.client.get(reverse('supply:requests_report'), {
                'export': 'csv',
                'filename': payload
            })
            
            # Should handle gracefully
            self.assertIn(response.status_code, [200, 400, 404])

    def test_session_security(self):
        """Test session security measures"""
        # Login and get session
        self.client.login(username='dept_user', password='testpass123')
        
        # Check that session is created
        self.assertIn('sessionid', self.client.cookies)
        
        # Session should have security flags in production
        # Note: This test would need to be adapted for production settings
        
        # Test session invalidation on logout
        self.client.logout()
        
        # Try to access protected resource after logout
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect to login

    def test_password_security(self):
        """Test password security requirements"""
        # Test weak password rejection (if implemented)
        weak_passwords = [
            '123456',
            'password',
            'admin',
            '12345678',
        ]
        
        for weak_password in weak_passwords:
            try:
                user = User.objects.create_user(
                    username=f'test_weak_{weak_password}',
                    password=weak_password
                )
                # If weak password is accepted, it should at least be hashed
                self.assertNotEqual(user.password, weak_password)
                user.delete()  # Clean up
            except Exception:
                # Password validation rejected weak password (good)
                pass

    def test_information_disclosure(self):
        """Test for information disclosure vulnerabilities"""
        # Test error pages don't reveal sensitive information
        response = self.client.get('/nonexistent-url/')
        self.assertEqual(response.status_code, 404)
        
        content = response.content.decode('utf-8')
        # Should not reveal system information
        sensitive_info = [
            'django',
            'python',
            'traceback',
            'exception',
            'database',
            'sql',
        ]
        
        for info in sensitive_info:
            self.assertNotIn(info.lower(), content.lower())

    def test_rate_limiting_simulation(self):
        """Simulate rate limiting tests"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Simulate rapid requests
        for i in range(10):
            response = self.client.get(reverse('supply:dashboard'))
            # Should handle multiple requests gracefully
            self.assertEqual(response.status_code, 200)

    def test_file_upload_security(self):
        """Test file upload security (if applicable)"""
        # Note: This application doesn't have file uploads,
        # but this is where you'd test for:
        # - File type validation
        # - File size limits
        # - Malicious file detection
        # - Path traversal in filenames
        pass

    def test_api_security(self):
        """Test API endpoint security"""
        # Test search API
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'test'
        })
        # Should require authentication
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test with authentication
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'test'
        })
        self.assertEqual(response.status_code, 200)
        
        # Response should be properly formatted JSON
        try:
            data = json.loads(response.content)
            self.assertIn('results', data)
        except json.JSONDecodeError:
            self.fail("API should return valid JSON")

    def test_input_validation(self):
        """Test input validation and sanitization"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Test invalid data types
        invalid_inputs = [
            {'quantity': 'not_a_number'},
            {'quantity': -1},  # Negative quantity
            {'quantity': 999999999},  # Extremely large number
            {'item': 'not_an_id'},
            {'item': 999999},  # Non-existent item
        ]
        
        for invalid_input in invalid_inputs:
            form_data = {
                'item': self.supply_item.id,
                'quantity': 10,
                'purpose': 'Valid purpose'
            }
            form_data.update(invalid_input)
            
            response = self.client.post(reverse('supply:request_create'), form_data)
            # Should either reject or handle gracefully
            self.assertIn(response.status_code, [200, 302, 400])

    def test_authorization_bypass_attempts(self):
        """Test attempts to bypass authorization"""
        # Department user tries to directly access GSO functions
        self.client.login(username='dept_user', password='testpass123')
        
        # Try to approve own request
        response = self.client.post(reverse('supply:approve_request', args=[self.test_request.id]), {
            'approval_remarks': 'Self approval attempt'
        })
        
        # Should be rejected
        self.assertIn(response.status_code, [302, 403])
        
        # Verify request was not approved
        self.test_request.refresh_from_db()
        self.assertEqual(self.test_request.status, 'PENDING')

    def test_data_exposure_in_responses(self):
        """Test that sensitive data is not exposed in responses"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:dashboard'))
        content = response.content.decode('utf-8')
        
        # Should not contain sensitive information
        sensitive_data = [
            'password',
            'secret',
            'token',
            'key',
            'csrf',  # CSRF tokens should be in forms, not visible
        ]
        
        for data in sensitive_data:
            # Some occurrences might be legitimate (like "password" in labels)
            # This is a basic check - in production, use more sophisticated scanning
            pass
