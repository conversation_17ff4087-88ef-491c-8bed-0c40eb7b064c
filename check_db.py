#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyItem, SupplyRequest
from django.contrib.auth.models import User

def main():
    print("=== Database Check ===")

    # Check for the specific request ID from the screenshot
    request_id = 'REQ-20250722-514D4BED'
    try:
        req = SupplyRequest.objects.get(request_id=request_id)
        print(f'Found request: {req.request_id}')
        print(f'Status: {req.status}')
        print(f'Item: {req.item.name if req.item else "No item"}')
        print(f'Item ID: {req.item.id if req.item else "None"}')
        print(f'Requester: {req.requester.username}')
        print(f'Approved by: {req.approved_by.username if req.approved_by else "None"}')
        print(f'Approved at: {req.approved_at}')
        print(f'Purpose: {req.purpose}')
        print(f'Quantity: {req.quantity}')
        print(f'Is batch request: {req.is_batch_request}')
    except SupplyRequest.DoesNotExist:
        print(f'Request {request_id} not found')

    # Check all pending requests
    pending_requests = SupplyRequest.objects.filter(status='PENDING')
    print(f'\nTotal pending requests: {pending_requests.count()}')

    for req in pending_requests[:5]:  # Show first 5
        print(f'  - {req.request_id}: {req.item.name if req.item else "No item"} (Qty: {req.quantity})')

    # Check for any auto-approved requests (approved but no approver)
    auto_approved = SupplyRequest.objects.filter(status='APPROVED', approved_by__isnull=True)
    print(f'\nAuto-approved requests (no approver): {auto_approved.count()}')

    for req in auto_approved:
        print(f'  - {req.request_id}: {req.item.name if req.item else "No item"} - APPROVED WITHOUT APPROVER!')

    # Check Floor Wax item
    floor_wax = SupplyItem.objects.filter(name__icontains='Floor Wax').first()
    if floor_wax:
        print(f'\nFloor Wax: ID={floor_wax.id}, Stock={floor_wax.current_stock}, Available={floor_wax.current_stock > 0}')

        # Check requests for Floor Wax
        floor_wax_requests = SupplyRequest.objects.filter(item=floor_wax)
        print(f'Floor Wax requests: {floor_wax_requests.count()}')
        for req in floor_wax_requests:
            print(f'  - {req.request_id}: Status={req.status}, Qty={req.quantity}')
    else:
        print('\nFloor Wax item not found')

    # Check all items that might have issues
    print(f'\nTotal supply items: {SupplyItem.objects.count()}')
    print(f'Total supply requests: {SupplyRequest.objects.count()}')

    # Check for requests with missing items
    requests_with_missing_items = SupplyRequest.objects.filter(item__isnull=True)
    print(f'Requests with missing items: {requests_with_missing_items.count()}')
    for req in requests_with_missing_items:
        print(f'  - {req.request_id}: Created={req.created_at}, Requester={req.requester.username}')

    # Check status distribution
    statuses = SupplyRequest.objects.values_list('status', flat=True)
    status_counts = {}
    for status in statuses:
        status_counts[status] = status_counts.get(status, 0) + 1

    print('\nRequest status distribution:')
    for status, count in status_counts.items():
        print(f'  - {status}: {count}')

    # Check if there are any deleted items that might have caused issues
    print('\nChecking for potential data integrity issues...')

    # Look for any items that might have been deleted recently
    from supply.models import InventoryTransaction
    recent_transactions = InventoryTransaction.objects.order_by('-created_at')[:10]
    print(f'Recent inventory transactions: {recent_transactions.count()}')
    for trans in recent_transactions:
        print(f'  - {trans.created_at}: {trans.transaction_type} {trans.quantity} of {trans.item.name}')

if __name__ == '__main__':
    main()
