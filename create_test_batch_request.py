#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest, SupplyRequestItem, SupplyItem
from django.contrib.auth.models import User
from django.db import transaction

def create_test_batch_request():
    print("=== Creating Test Batch Request ===")
    
    # Get mayor user (requester)
    mayor_user = User.objects.filter(username='mayor').first()
    if not mayor_user:
        print("Mayor user not found")
        return
    
    print(f"Requester: {mayor_user.username}")
    
    # Get some items for the batch request
    items_to_request = [
        ('Ballpoint Pen Blue', 2),
        ('Bond Paper A4', 1),
        ('Stapler', 1),
    ]
    
    print(f"Items to request:")
    available_items = []
    for item_name, quantity in items_to_request:
        item = SupplyItem.objects.filter(name=item_name).first()
        if item and item.current_stock >= quantity:
            available_items.append((item, quantity))
            print(f"  - {item.name}: {quantity} {item.unit} (Stock: {item.current_stock})")
        else:
            print(f"  - {item_name}: Not available or insufficient stock")
    
    if not available_items:
        print("No items available for batch request")
        return
    
    try:
        with transaction.atomic():
            # Create batch request
            batch_request = SupplyRequest.objects.create(
                requester=mayor_user,
                department=mayor_user.userprofile.department if hasattr(mayor_user, 'userprofile') else 'Mayor\'s Office',
                purpose='Test batch request for approval functionality testing',
                status='PENDING',
                is_batch_request=True
            )
            
            print(f"\nCreated batch request: {batch_request.request_id}")
            
            # Add items to the batch
            for item, quantity in available_items:
                SupplyRequestItem.objects.create(
                    request=batch_request,
                    item=item,
                    quantity=quantity
                )
                print(f"  Added: {item.name} x {quantity}")
            
            print(f"\n✓ Test batch request created successfully!")
            print(f"Request ID: {batch_request.request_id}")
            print(f"Status: {batch_request.status}")
            print(f"Items: {batch_request.request_items.count()}")
            print(f"URL: http://127.0.0.1:8000/gso/batch-requests/{batch_request.id}/")
            
            return batch_request
            
    except Exception as e:
        print(f"✗ Error creating batch request: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == '__main__':
    create_test_batch_request()
