# Generated by Django 4.2.17 on 2025-07-22 05:06

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('supply', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='supplyrequest',
            name='is_batch_request',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='supplyrequest',
            name='item',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='supply.supplyitem'),
        ),
        migrations.AlterField(
            model_name='supplyrequest',
            name='quantity',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='supplyrequest',
            name='status',
            field=models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('RELEASED', 'Released')], default='PENDING', max_length=20),
        ),
        migrations.CreateModel(
            name='SupplyRequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('approved_quantity', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('released_quantity', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('remarks', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='supply.supplyitem')),
                ('request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='request_items', to='supply.supplyrequest')),
            ],
            options={
                'verbose_name': 'Supply Request Item',
                'verbose_name_plural': 'Supply Request Items',
                'unique_together': {('request', 'item')},
            },
        ),
    ]
