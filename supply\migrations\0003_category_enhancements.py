# Generated by Django 4.2.17 on 2025-07-22 06:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('supply', '0002_batch_request_support'),
    ]

    operations = [
        migrations.CreateModel(
            name='SupplyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('color_code', models.CharField(default='#3B82F6', help_text='Hex color code for visual distinction', max_length=7)),
                ('icon_class', models.CharField(default='fas fa-box', help_text='Font Awesome icon class', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Supply Category',
                'verbose_name_plural': 'Supply Categories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='supplyitem',
            name='location',
            field=models.CharField(blank=True, help_text='Storage location', max_length=100),
        ),
        migrations.AddField(
            model_name='supplyitem',
            name='supplier',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='supplyitem',
            name='unit_cost',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='supplyitem',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items', to='supply.supplycategory'),
        ),
    ]
