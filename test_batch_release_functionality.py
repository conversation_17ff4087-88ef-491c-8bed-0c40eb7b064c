#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest
from django.contrib.auth.models import User

def test_batch_release_functionality():
    print("=== Testing Batch Release Functionality ===")
    
    # Get an approved batch request
    batch_request = SupplyRequest.objects.filter(is_batch_request=True, status='APPROVED').first()
    
    if not batch_request:
        print("No approved batch requests found")
        return
    
    print(f"\nTesting with request: {batch_request.request_id}")
    print(f"Current status: {batch_request.status}")
    print(f"Items: {batch_request.request_items.count()}")
    
    # Get GSO user
    gso_user = User.objects.filter(userprofile__role='GSO').first()
    if not gso_user:
        print("No GSO user found")
        return
    
    print(f"GSO user: {gso_user.username}")
    
    # Test release capability
    print(f"\n=== Pre-Release Check ===")
    can_release = batch_request.can_be_released()
    stock_status = batch_request.get_stock_status()
    
    print(f"Can be released: {can_release}")
    print(f"Stock status: {stock_status}")
    
    if not can_release:
        print("Cannot release this batch request - insufficient stock")
        return
    
    # Show current stock levels
    print(f"\n=== Current Stock Levels ===")
    for item in batch_request.request_items.all():
        approved_qty = item.approved_quantity or item.quantity
        print(f"- {item.item.name}: {item.item.current_stock} available, {approved_qty} to release")
    
    # Test the release process
    print(f"\n=== Testing Release Process ===")
    
    try:
        # Simulate release
        release_notes = "Test release via script"
        success = batch_request.release(gso_user, release_notes)
        
        if success:
            print("✓ Batch request released successfully!")
            
            print(f"\nAfter release:")
            print(f"  Status: {batch_request.status}")
            print(f"  Released by: {batch_request.released_by.username if batch_request.released_by else None}")
            print(f"  Released at: {batch_request.released_at}")
            print(f"  Release remarks: {batch_request.release_remarks}")
            
            print(f"\nUpdated stock levels:")
            for item in batch_request.request_items.all():
                approved_qty = item.approved_quantity or item.quantity
                print(f"- {item.item.name}: {item.item.current_stock} remaining (released {approved_qty})")
            
            print(f"\n✓ Batch release functionality works correctly!")
            print("The batch request should now appear as 'RELEASED' in the system.")
            
        else:
            print("✗ Failed to release batch request")
            
    except Exception as e:
        print(f"✗ Error during release: {e}")
        import traceback
        traceback.print_exc()
    
    # Reset for further testing
    reset = input("\nReset request to APPROVED for further testing? (y/n): ").lower().strip()
    if reset == 'y':
        # Reset the request status
        batch_request.status = 'APPROVED'
        batch_request.released_by = None
        batch_request.released_at = None
        batch_request.release_remarks = ''
        batch_request.save()
        
        # Reset stock levels (add back the released quantities)
        for item in batch_request.request_items.all():
            approved_qty = item.approved_quantity or item.quantity
            item.item.current_stock += approved_qty
            item.item.save()
        
        print("✓ Request reset to APPROVED status and stock levels restored")

if __name__ == '__main__':
    test_batch_release_functionality()
