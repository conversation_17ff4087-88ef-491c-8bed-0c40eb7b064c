# Design Document

## Overview

The Municipal Supply Request and Release Management System (MSRRMS) is designed as a Django-based web application with a modern, interactive frontend. The system follows a layered architecture with clear separation between data models, business logic, and presentation layers. The design emphasizes real-time interactivity, role-based access control, and efficient inventory management.

## Architecture

### System Architecture
The system follows a traditional Django MVC pattern enhanced with modern frontend technologies:

- **Backend**: Django 5.1.1 with SQLite database
- **Frontend**: Server-rendered HTML with Tailwind CSS, HTMX, Alpine.js, and Unpoly
- **Authentication**: Django's built-in authentication system with custom user profiles
- **Real-time Updates**: HTMX for seamless partial page updates
- **State Management**: Alpine.js for client-side interactivity

### Technology Stack Integration
- **Django**: Handles routing, business logic, database operations, and template rendering
- **HTMX**: Enables AJAX requests and partial page updates without JavaScript
- **Alpine.js**: Provides reactive data binding and client-side state management
- **Unpoly**: Enhances navigation and form handling with progressive enhancement
- **Tailwind CSS**: Utility-first CSS framework for responsive design

## Components and Interfaces

### Core Models

#### User Profile Extension
```python
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=[('DEPARTMENT', 'Department Staff'), ('GSO', 'GSO Staff')])
    department = models.CharField(max_length=100)
    phone = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

#### Supply Item
```python
class SupplyItem(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    unit = models.CharField(max_length=50)  # pieces, boxes, reams, etc.
    current_stock = models.IntegerField(default=0)
    minimum_stock = models.IntegerField(default=10)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### Supply Request
```python
class SupplyRequest(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('RELEASED', 'Released')
    ]
    
    request_id = models.CharField(max_length=20, unique=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE)
    department = models.CharField(max_length=100)
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    purpose = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    
    # Approval fields
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_requests')
    approved_at = models.DateTimeField(null=True, blank=True)
    approval_remarks = models.TextField(blank=True)
    
    # Release fields
    released_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='released_requests')
    released_at = models.DateTimeField(null=True, blank=True)
    release_remarks = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### Inventory Transaction
```python
class InventoryTransaction(models.Model):
    TRANSACTION_TYPES = [
        ('IN', 'Stock In'),
        ('OUT', 'Stock Out'),
        ('ADJUSTMENT', 'Adjustment')
    ]
    
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()
    reference_request = models.ForeignKey(SupplyRequest, on_delete=models.SET_NULL, null=True, blank=True)
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    remarks = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

### View Layer Architecture

#### Department Views
- **Dashboard**: Display user's requests with status filtering
- **Request Form**: Create new supply requests with item selection
- **Request Detail**: View individual request details and history

#### GSO Views
- **GSO Dashboard**: Overview of all requests with filtering and sorting
- **Request Management**: Approve/reject requests with inventory checking
- **Release Management**: Process approved requests and update inventory
- **Reports**: Generate various reports with filtering options
- **Inventory Management**: View and manage supply items and stock levels

### Frontend Component Structure

#### Base Template Structure
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSRRMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <!-- Main Content -->
    <!-- Footer -->
</body>
</html>
```

#### HTMX Integration Patterns
- **Partial Updates**: Use `hx-get`, `hx-post` for form submissions and data loading
- **Real-time Updates**: Implement `hx-trigger="every 30s"` for status updates
- **Form Validation**: Use `hx-target` for inline error display
- **Modal Dialogs**: HTMX-powered modals for confirmations and forms

#### Alpine.js State Management
- **Request Filtering**: Client-side filtering and sorting
- **Form Validation**: Real-time form validation feedback
- **UI State**: Toggle states, loading indicators, and notifications

## Data Models

### Entity Relationship Design

```mermaid
erDiagram
    User ||--|| UserProfile : has
    User ||--o{ SupplyRequest : creates
    User ||--o{ SupplyRequest : approves
    User ||--o{ SupplyRequest : releases
    SupplyItem ||--o{ SupplyRequest : requested_in
    SupplyItem ||--o{ InventoryTransaction : affects
    SupplyRequest ||--o| InventoryTransaction : generates
    User ||--o{ InventoryTransaction : performs

    User {
        int id PK
        string username
        string email
        string first_name
        string last_name
        datetime date_joined
    }

    UserProfile {
        int id PK
        int user_id FK
        string role
        string department
        string phone
        datetime created_at
    }

    SupplyItem {
        int id PK
        string name
        text description
        string unit
        int current_stock
        int minimum_stock
        datetime created_at
        datetime updated_at
    }

    SupplyRequest {
        int id PK
        string request_id
        int requester_id FK
        string department
        int item_id FK
        int quantity
        text purpose
        string status
        int approved_by_id FK
        datetime approved_at
        text approval_remarks
        int released_by_id FK
        datetime released_at
        text release_remarks
        datetime created_at
        datetime updated_at
    }

    InventoryTransaction {
        int id PK
        int item_id FK
        string transaction_type
        int quantity
        int reference_request_id FK
        int performed_by_id FK
        text remarks
        datetime created_at
    }
```

### Data Validation Rules
- **Supply Requests**: Quantity must be positive, purpose required, item must exist
- **Inventory**: Stock levels cannot go negative, transactions must be logged
- **User Roles**: Department users can only access their own requests
- **Approval Workflow**: Only GSO users can approve/reject/release requests

## Error Handling

### Application-Level Error Handling
- **Database Errors**: Graceful handling of connection issues and constraint violations
- **Validation Errors**: Clear user feedback for form validation failures
- **Permission Errors**: Appropriate redirects and error messages for unauthorized access
- **Inventory Errors**: Prevent negative stock with clear error messages

### Frontend Error Handling
- **HTMX Errors**: Use `hx-on::error` for handling AJAX failures
- **Form Validation**: Real-time validation with Alpine.js
- **Network Issues**: Graceful degradation when JavaScript fails
- **User Feedback**: Toast notifications for success/error states

### Error Response Patterns
```python
# View error handling example
def approve_request(request, request_id):
    try:
        supply_request = get_object_or_404(SupplyRequest, id=request_id)
        if supply_request.item.current_stock < supply_request.quantity:
            return JsonResponse({
                'error': 'Insufficient stock available',
                'current_stock': supply_request.item.current_stock,
                'requested': supply_request.quantity
            }, status=400)
        # Process approval...
    except Exception as e:
        return JsonResponse({'error': 'An error occurred processing your request'}, status=500)
```

## Testing Strategy

### Unit Testing
- **Model Tests**: Validate model methods, constraints, and relationships
- **View Tests**: Test view logic, permissions, and response handling
- **Form Tests**: Validate form processing and validation rules
- **Utility Tests**: Test helper functions and business logic

### Integration Testing
- **Workflow Tests**: Test complete request-to-release workflows
- **Permission Tests**: Verify role-based access control
- **Inventory Tests**: Test stock management and transaction logging
- **API Tests**: Test HTMX endpoints and JSON responses

### Frontend Testing
- **HTMX Integration**: Test partial page updates and form submissions
- **Alpine.js Components**: Test client-side state management
- **Responsive Design**: Test across different screen sizes
- **Accessibility**: Ensure WCAG compliance

### Test Data Management
- **Fixtures**: Create test data for different user roles and scenarios
- **Factory Pattern**: Use factory_boy for generating test objects
- **Database Isolation**: Ensure tests don't interfere with each other
- **Mock External Dependencies**: Mock any external services or APIs

### Performance Testing
- **Database Queries**: Optimize N+1 queries and use select_related/prefetch_related
- **Frontend Performance**: Test HTMX response times and Alpine.js reactivity
- **Load Testing**: Test system behavior under concurrent user load
- **Memory Usage**: Monitor memory consumption during extended use

## Security Considerations

### Authentication & Authorization
- **Django Authentication**: Use built-in authentication with session management
- **Role-Based Access**: Implement decorators for view-level permission checking
- **CSRF Protection**: Ensure all forms include CSRF tokens
- **Session Security**: Configure secure session settings

### Data Protection
- **Input Validation**: Sanitize all user inputs to prevent injection attacks
- **SQL Injection**: Use Django ORM to prevent SQL injection
- **XSS Prevention**: Use Django's template auto-escaping
- **File Upload Security**: If implemented, validate file types and sizes

### API Security
- **HTMX Endpoints**: Validate all AJAX requests and responses
- **Rate Limiting**: Implement rate limiting for API endpoints
- **Error Information**: Avoid exposing sensitive information in error messages
- **Logging**: Log security-relevant events for monitoring