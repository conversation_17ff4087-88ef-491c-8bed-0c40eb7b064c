{% extends 'base.html' %}

{% block title %}Low Stock Alerts{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <a href="{% url 'supply:inventory' %}" 
               class="text-blue-600 hover:text-blue-800 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Low Stock Alerts</h1>
        </div>
        <div class="flex items-center space-x-2">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                {{ low_stock_items.count }} Alert{{ low_stock_items.count|pluralize }}
            </span>
        </div>
    </div>

    {% if low_stock_items %}
    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Low Stock Alert</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>The following items are at or below their minimum stock levels and need attention.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul class="divide-y divide-gray-200">
            {% for item in low_stock_items %}
            <li>
                <div class="px-4 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4 flex-1">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">
                                            <a href="{% url 'supply:inventory_detail' item.id %}" class="hover:text-blue-600">
                                                {{ item.name }}
                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-500">{{ item.description|default:"No description" }}</p>
                                    </div>
                                </div>
                                
                                <div class="mt-2 flex items-center justify-between">
                                    <div class="flex items-center space-x-6">
                                        <div>
                                            <span class="text-sm text-gray-500">Current Stock:</span>
                                            <span class="ml-1 text-sm font-medium text-red-600">
                                                {{ item.current_stock }} {{ item.unit }}
                                            </span>
                                        </div>
                                        <div>
                                            <span class="text-sm text-gray-500">Minimum:</span>
                                            <span class="ml-1 text-sm font-medium text-gray-900">
                                                {{ item.minimum_stock }} {{ item.unit }}
                                            </span>
                                        </div>
                                        <div>
                                            <span class="text-sm text-gray-500">Need:</span>
                                            <span class="ml-1 text-sm font-medium text-red-600">
                                                {{ item.minimum_stock }} {{ item.unit }}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <a href="{% url 'supply:inventory_adjust' item.id %}" 
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            Add Stock
                                        </a>
                                        <a href="{% url 'supply:inventory_detail' item.id %}" 
                                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- Stock Level Bar -->
                                <div class="mt-3">
                                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                                        <span>Stock Level</span>
                                        <span>{{ item.current_stock }} / {{ item.minimum_stock|add:item.current_stock }} {{ item.unit }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        {% widthratio item.current_stock item.minimum_stock|add:item.current_stock 100 as stock_percentage %}
                                        <div class="h-2 rounded-full bg-red-600" 
                                             style="width: {{ stock_percentage|default:0 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
    </div>

    <div class="mt-6 bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'supply:inventory_add' %}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Item
            </a>
            
            <a href="{% url 'supply:inventory' %}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                View All Inventory
            </a>
            
            <a href="{% url 'supply:inventory_transactions' %}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                View Transactions
            </a>
        </div>
    </div>
    {% else %}
    <div class="text-center py-12">
        <div class="mx-auto h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No Low Stock Alerts</h3>
        <p class="mt-1 text-sm text-gray-500">All inventory items are currently above their minimum stock levels.</p>
        <div class="mt-6">
            <a href="{% url 'supply:inventory' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                View All Inventory
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}