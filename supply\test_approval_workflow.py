"""
Additional tests for the approval workflow to ensure comprehensive coverage.
"""
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.http import JsonResponse
from supply.models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction
from unittest.mock import patch
import json


class ApprovalWorkflowHTMXTestCase(TestCase):
    """Test HTMX functionality in approval workflow"""
    
    def setUp(self):
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON>',
            email='<EMAIL>'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            email='<EMAIL>'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test Description',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()
    
    def test_approve_request_htmx_response(self):
        """Test HTMX response for request approval"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing approval HTMX response'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'HTMX test approval'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        # Check for content that indicates the request detail page was rendered
        self.assertContains(response, 'Request Details')
        self.assertContains(response, request.request_id)
        
        # Verify request was approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'APPROVED')
        self.assertEqual(request.approval_remarks, 'HTMX test approval')
    
    def test_reject_request_htmx_response(self):
        """Test HTMX response for request rejection"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing rejection HTMX response'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': 'HTMX test rejection'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        # Check for content that indicates the request detail page was rendered
        self.assertContains(response, 'Request Details')
        self.assertContains(response, request.request_id)
        
        # Verify request was rejected
        request.refresh_from_db()
        self.assertEqual(request.status, 'REJECTED')
        self.assertEqual(request.approval_remarks, 'HTMX test rejection')
    
    def test_reject_request_htmx_error_response(self):
        """Test HTMX error response for rejection without remarks"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing rejection error HTMX response'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': ''},  # Empty remarks
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        # Check for error message content
        self.assertContains(response, 'Rejection reason is required')
        
        # Verify request was not rejected
        request.refresh_from_db()
        self.assertEqual(request.status, 'PENDING')
    
    def test_approve_insufficient_stock_htmx(self):
        """Test HTMX response when approving with insufficient stock"""
        # Set low stock
        self.item.current_stock = 5
        self.item.save()
        
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,  # More than available stock
            purpose='Testing insufficient stock approval'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Should fail due to insufficient stock'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect back to detail page
        
        # Verify request was not approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'PENDING')


class ApprovalNotificationTestCase(TestCase):
    """Test notification system for approval workflow"""
    
    def setUp(self):
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            password='testpass123',
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            email='<EMAIL>'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test Description',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()
    
    @patch('supply.views.send_mail')
    def test_approval_notification_sent(self, mock_send_mail):
        """Test that notification is sent when request is approved"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing approval notification'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Approved for testing'}
        )
        
        # Verify notification function was called
        mock_send_mail.assert_called_once()
        
        # Check the email content
        call_args = mock_send_mail.call_args
        subject = call_args[0][0]
        message = call_args[0][1]
        
        self.assertIn('approved', subject.lower())
        self.assertIn(request.request_id, message)
        self.assertIn('Approved for testing', message)
    
    @patch('supply.views.send_mail')
    def test_rejection_notification_sent(self, mock_send_mail):
        """Test that notification is sent when request is rejected"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing rejection notification'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': 'Not needed at this time'}
        )
        
        # Verify notification function was called
        mock_send_mail.assert_called_once()
        
        # Check the email content
        call_args = mock_send_mail.call_args
        subject = call_args[0][0]
        message = call_args[0][1]
        
        self.assertIn('rejected', subject.lower())
        self.assertIn(request.request_id, message)
        self.assertIn('Not needed at this time', message)


class ApprovalWorkflowEdgeCasesTestCase(TestCase):
    """Test edge cases in approval workflow"""
    
    def setUp(self):
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            password='testpass123',
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            email='<EMAIL>'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test Description',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()
    
    def test_approve_already_approved_request(self):
        """Test that already approved requests cannot be approved again"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing double approval'
        )
        
        # First approval
        request.approve(self.gso_user, 'First approval')
        
        self.client.login(username='gso_user', password='testpass123')
        
        # Try to approve again
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Second approval attempt'}
        )
        
        # Should redirect back with error message
        self.assertEqual(response.status_code, 302)
        
        # Verify request status hasn't changed
        request.refresh_from_db()
        self.assertEqual(request.status, 'APPROVED')
        self.assertEqual(request.approval_remarks, 'First approval')
    
    def test_reject_already_rejected_request(self):
        """Test that already rejected requests cannot be rejected again"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing double rejection'
        )
        
        # First rejection
        request.reject(self.gso_user, 'First rejection')
        
        self.client.login(username='gso_user', password='testpass123')
        
        # Try to reject again
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': 'Second rejection attempt'}
        )
        
        # Should redirect back with error message
        self.assertEqual(response.status_code, 302)
        
        # Verify request status hasn't changed
        request.refresh_from_db()
        self.assertEqual(request.status, 'REJECTED')
        self.assertEqual(request.approval_remarks, 'First rejection')
    
    def test_approve_released_request(self):
        """Test that released requests cannot be approved"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing approval of released request'
        )
        
        # Approve and release the request
        request.approve(self.gso_user, 'Initial approval')
        request.release(self.gso_user, 'Released for testing')
        
        self.client.login(username='gso_user', password='testpass123')
        
        # Try to approve the released request
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Trying to approve released request'}
        )
        
        # Should redirect back with error message
        self.assertEqual(response.status_code, 302)
        
        # Verify request status hasn't changed
        request.refresh_from_db()
        self.assertEqual(request.status, 'RELEASED')
    
    def test_concurrent_approval_attempts(self):
        """Test handling of concurrent approval attempts"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=10,
            purpose='Testing concurrent approval'
        )
        
        # Create second GSO user
        gso_user2 = User.objects.create_user(
            username='gso_user2',
            password='testpass123',
            first_name='Bob',
            last_name='Wilson'
        )
        gso_user2.userprofile.role = 'GSO'
        gso_user2.userprofile.department = 'General Services'
        gso_user2.userprofile.save()
        
        # First approval should succeed
        success1 = request.approve(self.gso_user, 'First approval')
        self.assertTrue(success1)
        
        # Second approval should fail
        success2 = request.approve(gso_user2, 'Second approval')
        self.assertFalse(success2)
        
        # Verify only first approval took effect
        request.refresh_from_db()
        self.assertEqual(request.approved_by, self.gso_user)
        self.assertEqual(request.approval_remarks, 'First approval')


class ApprovalWorkflowInventoryIntegrationTestCase(TestCase):
    """Test inventory integration with approval workflow"""
    
    def setUp(self):
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            password='testpass123',
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            email='<EMAIL>'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test Description',
            unit='pieces',
            current_stock=50,
            minimum_stock=10
        )
        
        self.client = Client()
    
    def test_approval_checks_current_inventory(self):
        """Test that approval checks current inventory levels"""
        # Create request for exact stock amount
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=50,
            purpose='Testing inventory check'
        )
        
        # Reduce stock after request creation
        self.item.current_stock = 30
        self.item.save()
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Should fail due to reduced stock'}
        )
        
        # Should redirect with error
        self.assertEqual(response.status_code, 302)
        
        # Verify request was not approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'PENDING')
    
    def test_approval_with_exact_stock_amount(self):
        """Test approval when requested quantity equals available stock"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=50,  # Exact stock amount
            purpose='Testing exact stock approval'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Approved for exact stock amount'}
        )
        
        # Should succeed
        self.assertEqual(response.status_code, 302)
        
        # Verify request was approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'APPROVED')
    
    def test_multiple_requests_inventory_validation(self):
        """Test inventory validation with multiple pending requests"""
        # Create two requests that together exceed stock (50 total stock)
        request1 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=30,
            purpose='First request'
        )
        
        request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=30,
            purpose='Second request'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        # Approve first request (30 out of 50 stock)
        response1 = self.client.post(
            reverse('supply:approve_request', args=[request1.id]),
            {'approval_remarks': 'First approval'}
        )
        self.assertEqual(response1.status_code, 302)
        
        # Verify first request approved
        request1.refresh_from_db()
        self.assertEqual(request1.status, 'APPROVED')
        
        # Second request should still be approvable since approval doesn't reduce stock
        # (stock is only reduced on release)
        response2 = self.client.post(
            reverse('supply:approve_request', args=[request2.id]),
            {'approval_remarks': 'Second approval'}
        )
        self.assertEqual(response2.status_code, 302)
        
        # Both requests should be approved (stock reduction happens at release)
        request2.refresh_from_db()
        self.assertEqual(request2.status, 'APPROVED')
        
        # However, when we try to release both, only one should succeed
        # Release first request
        response3 = self.client.post(
            reverse('supply:release_request', args=[request1.id]),
            {'release_remarks': 'First release'}
        )
        self.assertEqual(response3.status_code, 302)
        
        # Verify stock was reduced
        self.item.refresh_from_db()
        self.assertEqual(self.item.current_stock, 20)  # 50 - 30 = 20
        
        # Try to release second request (should fail due to insufficient stock)
        response4 = self.client.post(
            reverse('supply:release_request', args=[request2.id]),
            {'release_remarks': 'Second release'}
        )
        self.assertEqual(response4.status_code, 302)
        
        # Verify second request is still approved (not released)
        request2.refresh_from_db()
        self.assertEqual(request2.status, 'APPROVED')  # Still approved, not released