#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest, SupplyRequestItem
from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse

def test_batch_approval():
    print("=== Testing Batch Request Approval ===")
    
    # Get pending batch requests
    batch_requests = SupplyRequest.objects.filter(is_batch_request=True, status='PENDING')
    print(f"\nPending batch requests: {batch_requests.count()}")
    
    for req in batch_requests:
        print(f"\nRequest: {req.request_id}")
        print(f"  Status: {req.status}")
        print(f"  Items: {req.request_items.count()}")
        print(f"  Can approve: {req.status == 'PENDING'}")
        
        # Check inventory availability
        if req.request_items.exists():
            inventory_available = all(
                item.item.can_fulfill_quantity(item.quantity) 
                for item in req.request_items.all()
            )
            print(f"  Inventory available: {inventory_available}")
            
            for item in req.request_items.all():
                available = item.item.can_fulfill_quantity(item.quantity)
                print(f"    - {item.item.name}: {item.quantity} requested, {item.item.current_stock} available ({'✓' if available else '✗'})")
        
        # Test the view context
        print(f"  View context would be:")
        print(f"    - can_approve: {req.status == 'PENDING'}")
        print(f"    - can_release: {req.status == 'APPROVED'}")
        print(f"    - item_missing: {not req.request_items.exists()}")
    
    # Test URL generation
    print(f"\n=== URL Testing ===")
    for req in batch_requests[:1]:  # Test first request
        batch_url = f"/gso/batch-requests/{req.id}/"
        print(f"Batch request URL: {batch_url}")
        
        # Test if GSO user exists
        try:
            gso_user = User.objects.filter(userprofile__role='GSO').first()
            if gso_user:
                print(f"GSO user found: {gso_user.username}")
                
                # Create test client
                client = Client()
                login_success = client.login(username=gso_user.username, password='gso123')
                print(f"Login successful: {login_success}")
                
                if login_success:
                    # Test GET request to batch detail
                    response = client.get(batch_url)
                    print(f"GET {batch_url}: {response.status_code}")
                    
                    if response.status_code == 200:
                        print("✓ Batch request detail page loads successfully")
                        
                        # Check if approval form is in the response
                        content = response.content.decode()
                        has_approve_form = 'approve_all' in content
                        has_reject_form = 'reject_all' in content
                        print(f"✓ Approve form present: {has_approve_form}")
                        print(f"✓ Reject form present: {has_reject_form}")
                    else:
                        print(f"✗ Failed to load batch request detail page")
                else:
                    print("✗ Failed to login as GSO user")
            else:
                print("✗ No GSO user found")
        except Exception as e:
            print(f"✗ Error testing: {e}")

if __name__ == '__main__':
    test_batch_approval()
