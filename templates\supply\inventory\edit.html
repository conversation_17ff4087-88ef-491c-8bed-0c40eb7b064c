{% extends 'base_new.html' %}

{% block title %}Edit {{ item.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
            <a href="{% url 'supply:inventory_detail' item.id %}" 
               class="text-blue-600 hover:text-blue-800 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Edit {{ item.name }}</h1>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Item Name *
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.description.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Unit of Measurement *
                    </label>
                    {{ form.unit }}
                    {% if form.unit.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.unit.errors.0 }}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">e.g., pieces, boxes, reams, liters</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.current_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Current Stock *
                        </label>
                        {{ form.current_stock }}
                        {% if form.current_stock.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.current_stock.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">
                            Previous stock: {{ item.current_stock }} {{ item.unit }}
                        </p>
                    </div>

                    <div>
                        <label for="{{ form.minimum_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Stock Level *
                        </label>
                        {{ form.minimum_stock }}
                        {% if form.minimum_stock.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.minimum_stock.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Alert when stock falls below this level</p>
                    </div>
                </div>

                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                <strong>Note:</strong> Changing the current stock will create an inventory adjustment transaction.
                                For detailed stock adjustments, use the 
                                <a href="{% url 'supply:inventory_adjust' item.id %}" class="underline">Adjust Stock</a> feature instead.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6">
                    <a href="{% url 'supply:inventory_detail' item.id %}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Item
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}