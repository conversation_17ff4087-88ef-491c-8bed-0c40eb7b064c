<!-- Real-time status updates for HTMX -->
{% for request in requests %}
<script>
    // Update status badge for request {{ request.id }}
    const statusElement = document.querySelector('#request-{{ request.id }} .status-badge');
    if (statusElement) {
        let statusHtml = '';
        {% if request.status == 'PENDING' %}
            statusHtml = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>';
        {% elif request.status == 'APPROVED' %}
            statusHtml = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Approved</span>';
        {% elif request.status == 'REJECTED' %}
            statusHtml = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Rejected</span>';
        {% elif request.status == 'RELEASED' %}
            statusHtml = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Released</span>';
        {% endif %}
        statusElement.innerHTML = statusHtml;
    }
</script>
{% endfor %}