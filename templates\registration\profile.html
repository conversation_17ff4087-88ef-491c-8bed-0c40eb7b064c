{% extends 'base_new.html' %}

{% block title %}Profile - MSRRMS{% endblock %}

{% block page_title %}Profile{% endblock %}
{% block mobile_title %}Profile{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Profile Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4">
                <span class="text-blue-600 font-bold text-xl">
                    {{ user.first_name|first|default:user.username|first|upper }}{{ user.last_name|first|upper }}
                </span>
            </div>
            <div>
                <h1 class="text-2xl font-bold">{{ user.get_full_name|default:user.username }}</h1>
                <p class="text-blue-100">
                    {% if user.userprofile.role == 'GSO' %}
                        General Supply Officer
                    {% else %}
                        {{ user.userprofile.department|default:"Department User" }}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="px-6 py-5">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">
                Profile Information
            </h3>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- First Name -->
                    <div class="space-y-2">
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            First Name
                        </label>
                        <div class="mt-1">
                            <input type="text"
                                   name="first_name"
                                   id="{{ form.first_name.id_for_label }}"
                                   value="{{ form.first_name.value|default:'' }}"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        </div>
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Last Name -->
                    <div class="space-y-2">
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Last Name
                        </label>
                        <div class="mt-1">
                            <input type="text"
                                   name="last_name"
                                   id="{{ form.last_name.id_for_label }}"
                                   value="{{ form.last_name.value|default:'' }}"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        </div>
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Email -->
                <div class="space-y-2">
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                        Email Address
                    </label>
                    <div class="mt-1">
                        <input type="email"
                               name="email"
                               id="{{ form.email.id_for_label }}"
                               value="{{ form.email.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Role (Read-only) -->
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                        </svg>
                        Role
                    </label>
                    <div class="mt-1">
                        <input type="text"
                               value="{{ user.userprofile.get_role_display }}"
                               readonly
                               class="block w-full px-3 py-2 border border-gray-300 bg-gray-50 text-gray-500 rounded-lg focus:outline-none sm:text-sm">
                    </div>
                    <p class="mt-1 text-sm text-gray-500 flex items-center">
                        <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Role cannot be changed. Contact administrator if needed.
                    </p>
                </div>

                <!-- Department -->
                <div class="space-y-2">
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Department
                    </label>
                    <div class="mt-1">
                        <input type="text"
                               name="department"
                               id="{{ form.department.id_for_label }}"
                               value="{{ form.department.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                    {% if form.department.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.department.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Phone -->
                <div class="space-y-2">
                    <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Phone Number
                    </label>
                    <div class="mt-1">
                        <input type="tel"
                               name="phone"
                               id="{{ form.phone.id_for_label }}"
                               value="{{ form.phone.value|default:'' }}"
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                    {% if form.phone.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Account Information -->
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 rounded-lg border border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Account Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center">
                            <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900">Username</p>
                                <p class="text-gray-600">{{ user.username }}</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v6a2 2 0 01-2 2H10a2 2 0 01-2-2v-6" />
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900">Date Joined</p>
                                <p class="text-gray-600">{{ user.date_joined|date:"F d, Y" }}</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="font-medium text-gray-900">Profile Created</p>
                                <p class="text-gray-600">{{ user.userprofile.created_at|date:"F d, Y" }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ form.non_field_errors }}
                </div>
                {% endif %}

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{% if user.userprofile.role == 'GSO' %}{% url 'supply:gso_dashboard' %}{% else %}{% url 'supply:dashboard' %}{% endif %}"
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Cancel
                    </a>
                    <button type="submit"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        Update Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}