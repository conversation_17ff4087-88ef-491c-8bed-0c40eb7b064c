#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest, SupplyRequestItem
from django.contrib.auth.models import User

def test_fixes():
    print("=== Testing Fixes ===")
    
    # Test 1: Check the problematic request
    request_id = 'REQ-20250722-514D4BED'
    try:
        req = SupplyRequest.objects.get(request_id=request_id)
        print(f"\n1. Problematic Request Check:")
        print(f"   Request ID: {req.request_id}")
        print(f"   Is batch request: {req.is_batch_request}")
        print(f"   Status: {req.status}")
        print(f"   Has items: {req.request_items.exists()}")
        print(f"   Item count: {req.request_items.count()}")
        
        if req.request_items.exists():
            print("   Items in request:")
            for item in req.request_items.all():
                print(f"     - {item.item.name}: {item.quantity} {item.item.unit}")
        
        # Test the view logic
        if req.is_batch_request:
            if req.request_items.exists():
                inventory_available = all(
                    item.item.can_fulfill_quantity(item.quantity) 
                    for item in req.request_items.all()
                )
                item_missing = False
                print(f"   Inventory available: {inventory_available}")
                print(f"   Item missing: {item_missing}")
            else:
                item_missing = True
                print(f"   Item missing: {item_missing} (empty batch request)")
        
    except SupplyRequest.DoesNotExist:
        print(f"   Request {request_id} not found")
    
    # Test 2: Check for auto-approved requests
    print(f"\n2. Auto-Approval Check:")
    auto_approved = SupplyRequest.objects.filter(status='APPROVED', approved_by__isnull=True)
    print(f"   Auto-approved requests without approver: {auto_approved.count()}")
    
    if auto_approved.exists():
        for req in auto_approved:
            print(f"     - {req.request_id}: Status={req.status}, Approved by={req.approved_by}")
    
    # Test 3: Check batch request handling
    print(f"\n3. Batch Request Handling:")
    batch_requests = SupplyRequest.objects.filter(is_batch_request=True)
    print(f"   Total batch requests: {batch_requests.count()}")
    
    for req in batch_requests:
        item_count = req.request_items.count()
        print(f"     - {req.request_id}: {item_count} items, Status={req.status}")
        
        if item_count == 0:
            print(f"       WARNING: Empty batch request found!")
    
    # Test 4: URL routing test
    print(f"\n4. URL Routing Test:")
    print("   For batch requests, should use 'batch_request_detail' URL")
    print("   For single requests, should use 'gso_request_detail' URL")
    
    for req in SupplyRequest.objects.all()[:3]:
        if req.is_batch_request:
            url_name = 'supply:batch_request_detail'
        else:
            url_name = 'supply:gso_request_detail'
        print(f"     - {req.request_id} ({'batch' if req.is_batch_request else 'single'}): {url_name}")
    
    print(f"\n=== Test Complete ===")

if __name__ == '__main__':
    test_fixes()
