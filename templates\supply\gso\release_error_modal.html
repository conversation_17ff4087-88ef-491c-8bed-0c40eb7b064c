<!-- Release Error Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Error Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </div>
        
        <!-- Title -->
        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">
            Release Failed
        </h3>
        
        <!-- Message -->
        <p class="text-sm text-gray-600 text-center mb-6">
            {{ error_message|default:"Failed to release supplies. Please try again." }}
        </p>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-3">
            <button type="button"
                    @click="show = false; setTimeout(() => { window.location.href = '{% url 'supply:release_management' %}'; }, 300)"
                    class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                Back to Release Management
            </button>
        </div>
    </div>
</div>

<!-- Auto-redirect after 5 seconds -->
<script>
    setTimeout(() => {
        window.location.href = '{% url 'supply:release_management' %}';
    }, 5000);
</script>
