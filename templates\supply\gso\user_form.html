{% extends 'base_new.html' %}

{% block title %}{{ action }} User - GSO Dashboard - MSRRMS{% endblock %}

{% block page_title %}{{ action }} User{% endblock %}
{% block mobile_title %}{{ action }} User{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ action }} User</h1>
                <p class="mt-1 text-lg text-gray-600">
                    {% if action == "Create" %}
                        Add a new department user to the system
                    {% else %}
                        Update user information for {{ user_obj.username }}
                    {% endif %}
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'supply:user_management' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Back to Users
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow-xl rounded-xl border border-gray-100">
        <form method="POST" action="{{ submit_url }}" class="space-y-6">
            {% csrf_token %}
            
            <div class="px-6 py-5">
                <h3 class="text-lg font-medium text-gray-900 mb-6">User Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Username -->
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Username <span class="text-red-500">*</span>
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-xs text-gray-500">Unique identifier for login</p>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- First Name -->
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Department -->
                    <div>
                        <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Department <span class="text-red-500">*</span>
                        </label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.department.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-xs text-gray-500">Municipal department name</p>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        {{ form.phone }}
                        {% if form.phone.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.phone.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-xs text-gray-500">Optional contact number</p>
                    </div>
                </div>
            </div>

            <!-- Password Section -->
            <div class="px-6 py-5 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-6">
                    {% if action == "Create" %}Password{% else %}Change Password{% endif %}
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Password -->
                    <div>
                        <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {% if action == "Create" %}Password <span class="text-red-500">*</span>{% else %}New Password{% endif %}
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.password.errors.0 }}
                            </div>
                        {% endif %}
                        {% if action == "Create" %}
                            <p class="mt-1 text-xs text-gray-500">Minimum 8 characters</p>
                        {% else %}
                            <p class="mt-1 text-xs text-gray-500">Leave blank to keep current password</p>
                        {% endif %}
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="{{ form.confirm_password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {% if action == "Create" %}Confirm Password <span class="text-red-500">*</span>{% else %}Confirm New Password{% endif %}
                        </label>
                        {{ form.confirm_password }}
                        {% if form.confirm_password.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.confirm_password.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                {% if action == "Create" %}
                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Default Password</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>If no password is provided, the default password "municipal123" will be used. Users should change this on first login.</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Account Status -->
            <div class="px-6 py-5 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Account Status</h3>
                
                <div class="flex items-center">
                    {{ form.is_active }}
                    <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">
                        Active Account
                    </label>
                </div>
                <p class="mt-1 text-xs text-gray-500">Inactive users cannot log in to the system</p>
                {% if form.is_active.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.is_active.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="rounded-md bg-red-50 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Form Errors</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        {% for error in form.non_field_errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-xl">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'supply:user_management' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ action }} User
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
