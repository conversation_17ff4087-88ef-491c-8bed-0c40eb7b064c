<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MSRRMS - Municipal Supply Request and Release Management System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">
    
    <!-- Custom styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Loading animations */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        .htmx-request.htmx-indicator {
            opacity: 1;
        }

        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* Pulse animation for loading states */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* Notification animations */
        .notification-enter {
            transform: translateX(100%);
            opacity: 0;
        }
        .notification-enter-active {
            transform: translateX(0);
            opacity: 1;
            transition: all 0.3s ease-out;
        }
        .notification-exit {
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease-in;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen" x-data="globalApp()" x-init="init()">
    <!-- Global Loading Indicator -->
    <div id="global-loading" class="htmx-indicator fixed top-0 left-0 w-full h-1 bg-blue-600 z-50">
        <div class="h-full bg-blue-400 animate-pulse"></div>
    </div>

    <!-- Global Notification System -->
    <div id="notification-container"
         class="fixed top-4 right-4 z-50 space-y-2"
         x-show="notifications.length > 0"
         x-transition>
        <template x-for="notification in notifications" :key="notification.id">
            <div class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
                 x-show="notification.visible"
                 x-transition:enter="transform ease-out duration-300 transition"
                 x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
                 x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
                 x-transition:leave="transition ease-in duration-100"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg x-show="notification.type === 'success'" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <svg x-show="notification.type === 'error'" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <svg x-show="notification.type === 'warning'" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <svg x-show="notification.type === 'info'" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                            <p class="mt-1 text-sm text-gray-500" x-text="notification.message"></p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="removeNotification(notification.id)"
                                    class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Navigation -->
    <nav class="bg-blue-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-white text-xl font-bold">MSRRMS</h1>
                    </div>
                    {% if user.is_authenticated %}
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="{% url 'supply:dashboard' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                            {% if user.userprofile.role == 'GSO' %}
                            <a href="{% url 'supply:gso_dashboard' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">GSO Dashboard</a>
                            <a href="{% url 'supply:release_management' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Releases</a>
                            <a href="{% url 'supply:inventory' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Inventory</a>
                            <a href="{% url 'supply:reports' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Reports</a>
                            {% else %}
                            <a href="{% url 'supply:request_create' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">New Request</a>
                            <a href="{% url 'supply:request_history' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">My Requests</a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Global Search -->
                {% if user.is_authenticated %}
                <div class="flex-1 max-w-lg mx-8" x-data="globalSearch()">
                    <div class="relative">
                        <input type="text"
                               x-model="query"
                               @input.debounce.300ms="search"
                               @focus="showResults = true"
                               @keydown.escape="showResults = false"
                               placeholder="Search requests, items..."
                               class="w-full bg-blue-500 text-white placeholder-blue-200 border border-blue-400 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:bg-white focus:text-gray-900 focus:placeholder-gray-500 transition-all">

                        <!-- Search Results Dropdown -->
                        <div x-show="showResults && results.length > 0"
                             x-transition
                             @click.away="showResults = false"
                             class="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
                            <template x-for="result in results" :key="result.id">
                                <a :href="result.url"
                                   @click="showResults = false"
                                   class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="text-sm font-medium text-gray-900" x-text="result.title"></div>
                                            <div class="text-sm text-gray-500" x-text="result.subtitle"></div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                  :class="result.type === 'request' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'"
                                                  x-text="result.status"></span>
                                        </div>
                                    </div>
                                </a>
                            </template>
                        </div>

                        <!-- No Results -->
                        <div x-show="showResults && query.length > 0 && results.length === 0 && !loading"
                             x-transition
                             class="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                            <div class="px-4 py-3 text-sm text-gray-500 text-center">
                                No results found for "<span x-text="query"></span>"
                            </div>
                        </div>

                        <!-- Loading -->
                        <div x-show="loading"
                             class="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                            <div class="px-4 py-3 text-sm text-gray-500 text-center">
                                <svg class="animate-spin h-4 w-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Searching...
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if user.is_authenticated %}
                <div class="flex items-center space-x-4">
                    <div class="text-white text-sm">
                        Welcome, {{ user.first_name|default:user.username }}
                        {% if user.userprofile %}
                        <span class="text-blue-200">({{ user.userprofile.get_role_display }})</span>
                        {% endif %}
                    </div>
                    <a href="{% url 'supply:profile' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Profile</a>
                    <a href="{% url 'logout' %}" class="bg-blue-700 hover:bg-blue-800 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
                {% else %}
                <div class="flex items-center space-x-2">
                    <a href="{% url 'login' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Login</a>
                    <a href="{% url 'register' %}" class="bg-blue-700 hover:bg-blue-800 text-white px-3 py-2 rounded-md text-sm font-medium">Register</a>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Hidden Django Messages for Alpine.js conversion -->
    {% if messages %}
    <div style="display: none;">
        {% for message in messages %}
        <div class="django-message" data-type="{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-auto">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-sm">
                <p>&copy; 2024 Municipal Supply Request and Release Management System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    {% block extra_scripts %}{% endblock %}

    <!-- Global Alpine.js App -->
    <script>
        function globalApp() {
            return {
                notifications: [],
                notificationId: 0,

                init() {
                    // Set up HTMX event listeners
                    document.body.addEventListener('htmx:beforeRequest', () => {
                        this.showLoading();
                    });

                    document.body.addEventListener('htmx:afterRequest', () => {
                        this.hideLoading();
                    });

                    document.body.addEventListener('htmx:responseError', (event) => {
                        this.addNotification('error', 'Request Failed', 'An error occurred while processing your request.');
                    });

                    document.body.addEventListener('htmx:sendError', (event) => {
                        this.addNotification('error', 'Network Error', 'Unable to connect to the server. Please check your connection.');
                    });

                    // Listen for custom notification events
                    document.body.addEventListener('show-notification', (event) => {
                        const { type, title, message } = event.detail;
                        this.addNotification(type, title, message);
                    });

                    // Convert Django messages to notifications
                    this.convertDjangoMessages();
                },

                showLoading() {
                    document.getElementById('global-loading').style.display = 'block';
                },

                hideLoading() {
                    document.getElementById('global-loading').style.display = 'none';
                },

                addNotification(type, title, message, duration = 5000) {
                    const id = ++this.notificationId;
                    const notification = {
                        id,
                        type,
                        title,
                        message,
                        visible: true
                    };

                    this.notifications.push(notification);

                    // Auto-remove after duration
                    setTimeout(() => {
                        this.removeNotification(id);
                    }, duration);
                },

                removeNotification(id) {
                    const index = this.notifications.findIndex(n => n.id === id);
                    if (index > -1) {
                        this.notifications[index].visible = false;
                        setTimeout(() => {
                            this.notifications.splice(index, 1);
                        }, 300); // Wait for exit animation
                    }
                },

                convertDjangoMessages() {
                    // Convert Django messages to notifications
                    const messageElements = document.querySelectorAll('.django-message');
                    messageElements.forEach(element => {
                        const type = element.dataset.type || 'info';
                        const message = element.textContent.trim();

                        let title = 'Notification';
                        if (type === 'success') title = 'Success';
                        else if (type === 'error') title = 'Error';
                        else if (type === 'warning') title = 'Warning';
                        else if (type === 'info') title = 'Information';

                        this.addNotification(type, title, message);
                        element.remove(); // Remove the original message
                    });
                }
            }
        }

        // Global notification helper function
        window.showNotification = function(type, title, message) {
            document.body.dispatchEvent(new CustomEvent('show-notification', {
                detail: { type, title, message }
            }));
        };

        // Global search component
        function globalSearch() {
            return {
                query: '',
                results: [],
                showResults: false,
                loading: false,

                async search() {
                    if (this.query.length < 2) {
                        this.results = [];
                        this.showResults = false;
                        return;
                    }

                    this.loading = true;

                    try {
                        const response = await fetch(`{% url 'supply:global_search' %}?q=${encodeURIComponent(this.query)}`);
                        const data = await response.json();
                        this.results = data.results;
                        this.showResults = true;
                    } catch (error) {
                        console.error('Search error:', error);
                        this.results = [];
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }

        // HTMX configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Configure HTMX to use global loading indicator
            htmx.config.requestClass = 'htmx-request';
            htmx.config.indicatorClass = 'htmx-indicator';

            // Add CSRF token to all HTMX requests
            document.body.addEventListener('htmx:configRequest', function(event) {
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfToken) {
                    event.detail.headers['X-CSRFToken'] = csrfToken.value;
                }
            });
        });
    </script>
</body>
</html>