from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from .models import UserProfile, SupplyItem, SupplyRequest, AuditLog
from .audit import log_audit_event, log_request_action


class AdvancedFeaturesTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply items
        self.supply_item1 = SupplyItem.objects.create(
            name='Test Item 1',
            description='First test item for searching',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.supply_item2 = SupplyItem.objects.create(
            name='Low Stock Item',
            description='Item with low stock',
            unit='boxes',
            current_stock=5,  # Below minimum
            minimum_stock=10
        )
        
        # Create supply requests
        self.request1 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item1,
            quantity=10,
            purpose='Testing search functionality',
            status='PENDING'
        )
        
        self.request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item2,
            quantity=5,
            purpose='Another test request',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )
        
        self.client = Client()

    def test_global_search_functionality(self):
        """Test global search for requests and items"""
        # Test GSO search
        self.client.login(username='gso_user', password='testpass123')
        
        # Search for requests
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': 'test'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('results', data)
        self.assertTrue(len(data['results']) > 0)
        
        # Check that both requests and items are returned
        result_types = [result['type'] for result in data['results']]
        self.assertIn('request', result_types)
        self.assertIn('item', result_types)

    def test_department_user_search_restrictions(self):
        """Test that department users can only search their own requests"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': 'test'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Should only return requests, not items
        result_types = [result['type'] for result in data['results']]
        self.assertIn('request', result_types)
        self.assertNotIn('item', result_types)

    def test_audit_logging_functionality(self):
        """Test audit logging system"""
        # Test manual audit logging
        log_audit_event(
            user=self.gso_user,
            action_type='CREATE',
            obj=self.request1,
            changes={'status': 'PENDING'},
            additional_data={'test': 'data'}
        )
        
        # Check that audit log was created
        audit_logs = AuditLog.objects.filter(user=self.gso_user)
        self.assertEqual(audit_logs.count(), 1)
        
        audit_log = audit_logs.first()
        self.assertEqual(audit_log.action_type, 'CREATE')
        self.assertEqual(audit_log.object_type, 'SupplyRequest')
        self.assertEqual(audit_log.object_id, str(self.request1.id))
        
        # Test JSON serialization
        changes = audit_log.get_changes()
        self.assertEqual(changes['status'], 'PENDING')
        
        additional_data = audit_log.get_additional_data()
        self.assertEqual(additional_data['test'], 'data')

    def test_audit_logging_on_approval(self):
        """Test that audit logging works on request approval"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Approve a request
        response = self.client.post(
            reverse('supply:approve_request', args=[self.request1.id]),
            {'approval_remarks': 'Test approval'}
        )
        
        # Check that audit log was created
        audit_logs = AuditLog.objects.filter(
            user=self.gso_user,
            action_type='APPROVE'
        )
        self.assertTrue(audit_logs.exists())

    def test_dashboard_widgets_recent_activity(self):
        """Test dashboard widgets for recent activity"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'recent_activity'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('data', data)
        self.assertTrue(len(data['data']) > 0)

    def test_dashboard_widgets_low_stock_alerts(self):
        """Test dashboard widgets for low stock alerts"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'low_stock_alerts'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('data', data)
        
        # Should include the low stock item
        item_names = [item['name'] for item in data['data']]
        self.assertIn('Low Stock Item', item_names)

    def test_dashboard_widgets_access_control(self):
        """Test that department users cannot access GSO-only widgets"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Try to access low stock alerts (GSO only)
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'low_stock_alerts'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['data'], [])  # Should be empty for non-GSO users

    def test_dashboard_widgets_statistics(self):
        """Test dashboard statistics widget"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'statistics'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('data', data)
        
        stats = data['data']
        self.assertIn('total_requests', stats)
        self.assertIn('pending_requests', stats)
        self.assertIn('total_items', stats)
        self.assertIn('low_stock_items', stats)

    def test_dashboard_widgets_pending_approvals(self):
        """Test pending approvals widget"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'pending_approvals'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('data', data)
        
        # Should include the pending request
        request_ids = [req['request_id'] for req in data['data']]
        self.assertIn(self.request1.request_id, request_ids)

    def test_search_empty_query(self):
        """Test search with empty query"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': ''}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['results'], [])

    def test_search_no_results(self):
        """Test search with query that returns no results"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': 'nonexistent'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['results'], [])

    def test_audit_log_json_methods(self):
        """Test AuditLog JSON serialization methods"""
        audit_log = AuditLog.objects.create(
            user=self.gso_user,
            action_type='TEST',
            object_type='TestObject',
            object_id='123',
            object_repr='Test Object'
        )
        
        # Test setting and getting changes
        test_changes = {'field1': 'old_value', 'field2': 'new_value'}
        audit_log.set_changes(test_changes)
        audit_log.save()
        
        retrieved_changes = audit_log.get_changes()
        self.assertEqual(retrieved_changes, test_changes)
        
        # Test setting and getting additional data
        test_data = {'key1': 'value1', 'key2': 'value2'}
        audit_log.set_additional_data(test_data)
        audit_log.save()
        
        retrieved_data = audit_log.get_additional_data()
        self.assertEqual(retrieved_data, test_data)

    def test_invalid_widget_type(self):
        """Test dashboard widgets with invalid widget type"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:dashboard_widgets'),
            {'widget': 'invalid_widget'}
        )
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn('error', data)

    def test_search_by_request_id(self):
        """Test searching by request ID"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': self.request1.request_id}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Should find the specific request
        request_ids = [result['title'] for result in data['results'] if result['type'] == 'request']
        self.assertTrue(any(self.request1.request_id in title for title in request_ids))

    def test_search_by_department(self):
        """Test searching by department name"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:global_search'),
            {'q': 'IT Department'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Should find requests from IT Department
        self.assertTrue(len(data['results']) > 0)
