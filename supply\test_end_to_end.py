from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from .models import UserProfile, SupplyItem, SupplyRequest
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from django.test import override_settings
import time


class EndToEndTestCase(TestCase):
    """End-to-end tests for complete user scenarios"""
    
    def setUp(self):
        """Set up test data for end-to-end scenarios"""
        # Create users
        self.gso_user = User.objects.create_user(
            username='gso_admin',
            email='<EMAIL>',
            password='SecurePass123!',
            first_name='<PERSON>',
            last_name='Admin'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        self.dept_user = User.objects.create_user(
            username='it_manager',
            email='<EMAIL>',
            password='SecurePass123!',
            first_name='<PERSON>',
            last_name='Manager'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply items
        self.office_supplies = SupplyItem.objects.create(
            name='Office Paper A4',
            description='White office paper, 80gsm, A4 size',
            unit='reams',
            current_stock=50,
            minimum_stock=10
        )
        
        self.printer_toner = SupplyItem.objects.create(
            name='Printer Toner Cartridge',
            description='Black toner cartridge for HP LaserJet',
            unit='pieces',
            current_stock=5,
            minimum_stock=3
        )
        
        self.low_stock_item = SupplyItem.objects.create(
            name='USB Flash Drives',
            description='16GB USB 3.0 flash drives',
            unit='pieces',
            current_stock=2,  # Below minimum
            minimum_stock=5
        )
        
        self.client = Client()

    def test_new_employee_first_request_scenario(self):
        """Test scenario: New employee making their first supply request"""
        # Employee logs in for the first time
        login_success = self.client.login(username='it_manager', password='SecurePass123!')
        self.assertTrue(login_success)
        
        # Employee views dashboard and sees welcome message
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Welcome')
        self.assertContains(response, 'Jane Manager')
        
        # Employee navigates to create new request
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Supply Request')
        
        # Employee fills out request form
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.office_supplies.id,
            'quantity': 10,
            'purpose': 'Monthly office supplies for IT department team meetings and documentation'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        
        # Verify request was created
        request = SupplyRequest.objects.filter(
            requester=self.dept_user,
            item=self.office_supplies
        ).first()
        self.assertIsNotNone(request)
        self.assertEqual(request.status, 'PENDING')
        
        # Employee views their request history
        response = self.client.get(reverse('supply:request_history'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, request.request_id)
        self.assertContains(response, 'Pending')

    def test_gso_daily_workflow_scenario(self):
        """Test scenario: GSO administrator's daily workflow"""
        # Create some pending requests first
        request1 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.office_supplies,
            quantity=5,
            purpose='Office supplies for new project',
            status='PENDING'
        )
        
        request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.printer_toner,
            quantity=2,
            purpose='Replacement toner for department printer',
            status='PENDING'
        )
        
        # GSO admin logs in
        login_success = self.client.login(username='gso_admin', password='SecurePass123!')
        self.assertTrue(login_success)
        
        # GSO views dashboard and sees pending requests
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GSO Dashboard')
        self.assertContains(response, request1.request_id)
        self.assertContains(response, request2.request_id)
        
        # GSO checks low stock alerts
        response = self.client.get(reverse('supply:dashboard_widgets'), {
            'widget': 'low_stock_alerts'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        # Should show USB Flash Drives as low stock
        low_stock_names = [item['name'] for item in data['data']]
        self.assertIn('USB Flash Drives', low_stock_names)
        
        # GSO reviews and approves first request
        response = self.client.get(reverse('supply:gso_request_detail', args=[request1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, request1.purpose)
        
        response = self.client.post(reverse('supply:approve_request', args=[request1.id]), {
            'approval_remarks': 'Approved - standard office supplies request'
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify approval
        request1.refresh_from_db()
        self.assertEqual(request1.status, 'APPROVED')
        self.assertEqual(request1.approved_by, self.gso_user)
        
        # GSO releases supplies for approved request
        initial_stock = self.office_supplies.current_stock
        response = self.client.post(reverse('supply:release_request', args=[request1.id]), {
            'release_remarks': 'Released - supplies ready for pickup'
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify release and inventory update
        request1.refresh_from_db()
        self.assertEqual(request1.status, 'RELEASED')
        
        self.office_supplies.refresh_from_db()
        self.assertEqual(self.office_supplies.current_stock, initial_stock - 5)
        
        # GSO performs bulk approval for efficiency
        request3 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.printer_toner,
            quantity=1,
            purpose='Additional toner backup',
            status='PENDING'
        )
        
        response = self.client.post(reverse('supply:batch_operations'), {
            'action': 'bulk_approve',
            'selected_requests': [request2.id, request3.id],
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify bulk approval
        request2.refresh_from_db()
        request3.refresh_from_db()
        self.assertEqual(request2.status, 'APPROVED')
        self.assertEqual(request3.status, 'APPROVED')

    def test_urgent_request_scenario(self):
        """Test scenario: Urgent supply request requiring immediate attention"""
        # Department user creates urgent request
        self.client.login(username='it_manager', password='SecurePass123!')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.printer_toner.id,
            'quantity': 1,
            'purpose': 'URGENT: Printer toner needed for board meeting presentations tomorrow'
        })
        
        urgent_request = SupplyRequest.objects.filter(
            purpose__contains='URGENT'
        ).first()
        self.assertIsNotNone(urgent_request)
        
        # GSO admin sees urgent request and processes immediately
        self.client.login(username='gso_admin', password='SecurePass123!')
        
        # Quick approval
        response = self.client.post(reverse('supply:approve_request', args=[urgent_request.id]), {
            'approval_remarks': 'URGENT APPROVAL - Board meeting requirement'
        })
        self.assertEqual(response.status_code, 302)
        
        # Immediate release
        response = self.client.post(reverse('supply:release_request', args=[urgent_request.id]), {
            'release_remarks': 'URGENT RELEASE - Available for immediate pickup'
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify complete processing
        urgent_request.refresh_from_db()
        self.assertEqual(urgent_request.status, 'RELEASED')
        self.assertIsNotNone(urgent_request.released_at)

    def test_inventory_replenishment_scenario(self):
        """Test scenario: Inventory replenishment workflow"""
        self.client.login(username='gso_admin', password='SecurePass123!')
        
        # GSO notices low stock item
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'USB Flash Drives')
        
        # GSO views item details
        response = self.client.get(reverse('supply:inventory_detail', args=[self.low_stock_item.id]))
        self.assertEqual(response.status_code, 200)
        
        # GSO adjusts stock after receiving new shipment
        initial_stock = self.low_stock_item.current_stock
        response = self.client.post(reverse('supply:inventory_adjust', args=[self.low_stock_item.id]), {
            'adjustment_type': 'IN',
            'quantity': 20,
            'remarks': 'New shipment received from vendor - Purchase Order #2024-001'
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify stock adjustment
        self.low_stock_item.refresh_from_db()
        self.assertEqual(self.low_stock_item.current_stock, initial_stock + 20)
        
        # Verify item is no longer in low stock alerts
        response = self.client.get(reverse('supply:dashboard_widgets'), {
            'widget': 'low_stock_alerts'
        })
        data = response.json()
        low_stock_names = [item['name'] for item in data['data']]
        self.assertNotIn('USB Flash Drives', low_stock_names)

    def test_monthly_reporting_scenario(self):
        """Test scenario: Monthly reporting and analysis"""
        # Create historical data
        self.client.login(username='it_manager', password='SecurePass123!')
        
        # Create and process several requests
        for i in range(5):
            response = self.client.post(reverse('supply:request_create'), {
                'item': self.office_supplies.id,
                'quantity': 2,
                'purpose': f'Monthly supplies batch {i+1}'
            })
        
        # GSO processes requests
        self.client.login(username='gso_admin', password='SecurePass123!')
        
        requests = SupplyRequest.objects.filter(purpose__contains='Monthly supplies')
        for request in requests:
            request.approve(self.gso_user, 'Monthly batch approval')
            request.release(self.gso_user, 'Monthly batch release')
        
        # GSO generates reports
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Reports Dashboard')
        
        # Generate requests report
        response = self.client.get(reverse('supply:requests_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'IT Department')
        
        # Generate departmental usage report
        response = self.client.get(reverse('supply:departmental_usage_report'))
        self.assertEqual(response.status_code, 200)
        
        # Export report to CSV
        response = self.client.get(reverse('supply:requests_report'), {
            'export': 'csv'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

    def test_system_search_and_navigation_scenario(self):
        """Test scenario: User searching and navigating the system"""
        # Create test data
        self.client.login(username='it_manager', password='SecurePass123!')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.office_supplies.id,
            'quantity': 3,
            'purpose': 'Quarterly documentation printing supplies'
        })
        
        search_request = SupplyRequest.objects.filter(
            purpose__contains='Quarterly'
        ).first()
        
        # User searches for their request
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'quarterly'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(len(data['results']) > 0)
        
        # User searches for items
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'paper'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(len(data['results']) > 0)
        
        # GSO user has broader search access
        self.client.login(username='gso_admin', password='SecurePass123!')
        
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'office'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Should return both requests and items
        result_types = [result['type'] for result in data['results']]
        self.assertIn('request', result_types)
        self.assertIn('item', result_types)

    def test_error_handling_and_recovery_scenario(self):
        """Test scenario: Error handling and system recovery"""
        self.client.login(username='it_manager', password='SecurePass123!')
        
        # Test invalid request creation
        response = self.client.post(reverse('supply:request_create'), {
            'item': 99999,  # Non-existent item
            'quantity': 10,
            'purpose': 'Test invalid item'
        })
        # Should handle gracefully without crashing
        self.assertIn(response.status_code, [200, 302])
        
        # Test request for more than available stock
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.low_stock_item.id,
            'quantity': 100,  # More than available
            'purpose': 'Test excessive quantity'
        })
        # Should still create request (validation happens at approval)
        self.assertEqual(response.status_code, 302)
        
        excessive_request = SupplyRequest.objects.filter(
            purpose='Test excessive quantity'
        ).first()
        self.assertIsNotNone(excessive_request)
        
        # GSO tries to release without sufficient stock
        self.client.login(username='gso_admin', password='SecurePass123!')
        
        excessive_request.approve(self.gso_user, 'Test approval')
        
        response = self.client.post(reverse('supply:release_request', args=[excessive_request.id]), {
            'release_remarks': 'Test release'
        })
        # Should handle insufficient stock gracefully
        self.assertEqual(response.status_code, 302)
        
        excessive_request.refresh_from_db()
        # Should remain approved, not released
        self.assertEqual(excessive_request.status, 'APPROVED')
