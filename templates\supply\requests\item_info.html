<!-- Item Information Display for HTMX -->
{% if item %}
<div class="bg-gray-50 border border-gray-200 rounded-md p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-gray-800">
                {{ item.name }}
            </h3>
            <div class="mt-2 text-sm text-gray-700">
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                        <span class="font-medium">Available Stock:</span>
                        <span class="{% if item.current_stock <= item.minimum_stock %}text-red-600 font-semibold{% endif %}">
                            {{ item.current_stock }} {{ item.unit }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">Unit:</span>
                        {{ item.unit }}
                    </div>
                    <div>
                        <span class="font-medium">Minimum Stock:</span>
                        {{ item.minimum_stock }} {{ item.unit }}
                    </div>
                </div>
                {% if item.description %}
                <div class="mt-2">
                    <span class="font-medium">Description:</span>
                    {{ item.description }}
                </div>
                {% endif %}
                {% if item.current_stock <= item.minimum_stock %}
                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-2">
                            <p class="text-sm text-yellow-700">
                                <strong>Low Stock Alert:</strong> This item is at or below minimum stock level.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}