{% extends 'base_new.html' %}

{% block title %}Inventory Report - MSRRMS{% endblock %}

{% block content %}
<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <div>
        <h3 class="text-gray-700 text-3xl font-medium">Inventory Report</h3>
        <p class="text-gray-500 mt-1">Current inventory status and movements</p>
    </div>
    <a href="{% url 'supply:reports' %}" class="px-4 py-2 bg-gray-800 text-white text-sm font-medium rounded-md hover:bg-gray-700">
        Back to Reports
    </a>
</div>

<!-- Summary Statistics -->
<div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
    <div class="p-6 bg-white rounded-lg shadow-md flex items-center">
        <div class="p-3 mr-4 text-blue-500 bg-blue-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path></svg>
        </div>
        <div>
            <p class="text-sm font-medium text-gray-600">Total Items</p>
            <p class="text-2xl font-semibold text-gray-700">{{ total_items }}</p>
        </div>
    </div>
    <div class="p-6 bg-white rounded-lg shadow-md flex items-center">
        <div class="p-3 mr-4 text-red-500 bg-red-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>
        </div>
        <div>
            <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
            <p class="text-2xl font-semibold text-gray-700">{{ low_stock_count }}</p>
        </div>
    </div>
    <div class="p-6 bg-white rounded-lg shadow-md flex items-center">
        <div class="p-3 mr-4 text-green-500 bg-green-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
        </div>
        <div>
            <p class="text-sm font-medium text-gray-600">Total Stock Value</p>
            <p class="text-2xl font-semibold text-gray-700">{{ total_stock_value }}</p>
        </div>
    </div>
</div>

<!-- Filters and Export Card -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end">
        <div class="lg:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700">Search Items</label>
            <input type="text" name="search" id="search" value="{{ search_query }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
        </div>
        <div class="flex items-center pt-6">
            <input id="low_stock" name="low_stock" type="checkbox" value="true" {% if low_stock_only %}checked{% endif %} class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
            <label for="low_stock" class="ml-2 block text-sm text-gray-900">Show low stock only</label>
        </div>
        <div class="flex items-center space-x-2">
            <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">Filter</button>
            <a href="?{{ request.GET.urlencode }}&export=csv" class="w-full text-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">CSV</a>
            <a href="?{{ request.GET.urlencode }}&export=pdf" class="w-full text-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">PDF</a>
        </div>
    </form>
</div>

<!-- Inventory Table -->
<div class="flex flex-col mt-8">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                {% if page_obj %}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Level</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transactions (In/Out/Adj)</th>
                            <th scope="col" class="relative px-6 py-3"><span class="sr-only">View</span></th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                                <div class="text-sm text-gray-500">{{ item.description|truncatechars:40 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.category }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium {% if item.current_stock <= item.minimum_stock %}text-red-600{% else %}text-gray-900{% endif %}">
                                    {{ item.current_stock }} / {{ item.minimum_stock }} {{ item.unit }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if item.current_stock <= item.minimum_stock %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Low Stock</span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">In Stock</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.total_in|default:0 }} / {{ item.total_out|default:0 }} / {{ item.total_adjustments|default:0 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'supply:inventory_detail' item.id %}" class="text-indigo-600 hover:text-indigo-900">View</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&low_stock={{ low_stock_only|yesno:'true,' }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 {% if not page_obj.has_previous %}pointer-events-none opacity-50{% endif %}">Previous</a>
                        <a href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&low_stock={{ low_stock_only|yesno:'true,' }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 {% if not page_obj.has_next %}pointer-events-none opacity-50{% endif %}">Next</a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&low_stock={{ low_stock_only|yesno:'true,' }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 {% if not page_obj.has_previous %}pointer-events-none opacity-50{% endif %}">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
                                </a>
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span aria-current="page" class="relative z-10 inline-flex items-center px-4 py-2 border text-sm font-medium text-indigo-600 bg-indigo-50">{{ num }}</span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}&search={{ search_query }}&low_stock={{ low_stock_only|yesno:'true,' }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                                    {% endif %}
                                {% endfor %}
                                <a href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&low_stock={{ low_stock_only|yesno:'true,' }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 {% if not page_obj.has_next %}pointer-events-none opacity-50{% endif %}">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-12 px-6">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" /></svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No inventory items found</h3>
                    <p class="mt-1 text-sm text-gray-500">No items match the selected criteria.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
