from django.test import TestCase, Client, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.db import transaction
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction, AuditLog
import time


class IntegrationTestCase(TransactionTestCase):
    """Integration tests for complete workflows"""
    
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for integration testing',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()

    def test_complete_request_workflow(self):
        """Test complete workflow from request creation to release"""
        # Step 1: Department user creates a request
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 10,
            'purpose': 'Integration test request'
        })
        
        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)
        
        # Verify request was created
        request = SupplyRequest.objects.filter(purpose='Integration test request').first()
        self.assertIsNotNone(request)
        self.assertEqual(request.status, 'PENDING')
        self.assertEqual(request.requester, self.dept_user)
        
        # Step 2: GSO user views and approves the request
        self.client.login(username='gso_user', password='testpass123')
        
        # View GSO dashboard
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, request.request_id)
        
        # Approve the request
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Approved for integration test'}
        )
        self.assertEqual(response.status_code, 302)
        
        # Verify request was approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'APPROVED')
        self.assertEqual(request.approved_by, self.gso_user)
        self.assertIsNotNone(request.approved_at)
        
        # Verify audit log was created
        audit_logs = AuditLog.objects.filter(
            user=self.gso_user,
            action_type='APPROVE',
            object_id=str(request.id)
        )
        self.assertTrue(audit_logs.exists())
        
        # Step 3: GSO user releases the supplies
        initial_stock = self.supply_item.current_stock
        
        response = self.client.post(
            reverse('supply:release_request', args=[request.id]),
            {'release_remarks': 'Released for integration test'}
        )
        self.assertEqual(response.status_code, 302)
        
        # Verify request was released
        request.refresh_from_db()
        self.assertEqual(request.status, 'RELEASED')
        self.assertEqual(request.released_by, self.gso_user)
        self.assertIsNotNone(request.released_at)
        
        # Verify inventory was updated
        self.supply_item.refresh_from_db()
        self.assertEqual(self.supply_item.current_stock, initial_stock - 10)
        
        # Verify inventory transaction was created
        transaction = InventoryTransaction.objects.filter(
            reference_request=request,
            transaction_type='OUT'
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.quantity, 10)
        
        # Verify release audit log was created
        release_audit_logs = AuditLog.objects.filter(
            user=self.gso_user,
            action_type='RELEASE',
            object_id=str(request.id)
        )
        self.assertTrue(release_audit_logs.exists())
        
        # Step 4: Department user views their request history
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:request_history'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, request.request_id)
        self.assertContains(response, 'Released')

    def test_request_rejection_workflow(self):
        """Test workflow for request rejection"""
        # Create request as department user
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 10,
            'purpose': 'Request to be rejected'
        })
        
        request = SupplyRequest.objects.filter(purpose='Request to be rejected').first()
        self.assertIsNotNone(request)
        
        # Reject request as GSO user
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': 'Insufficient justification'}
        )
        self.assertEqual(response.status_code, 302)
        
        # Verify request was rejected
        request.refresh_from_db()
        self.assertEqual(request.status, 'REJECTED')
        self.assertEqual(request.approved_by, self.gso_user)  # approved_by is used for both approve and reject
        self.assertIsNotNone(request.approved_at)
        
        # Verify audit log was created
        audit_logs = AuditLog.objects.filter(
            user=self.gso_user,
            action_type='REJECT',
            object_id=str(request.id)
        )
        self.assertTrue(audit_logs.exists())

    def test_bulk_operations_workflow(self):
        """Test bulk operations workflow"""
        # Create multiple requests
        self.client.login(username='dept_user', password='testpass123')
        
        requests = []
        for i in range(3):
            response = self.client.post(reverse('supply:request_create'), {
                'item': self.supply_item.id,
                'quantity': 5,
                'purpose': f'Bulk test request {i+1}'
            })
            request = SupplyRequest.objects.filter(purpose=f'Bulk test request {i+1}').first()
            requests.append(request)
        
        # Bulk approve as GSO user
        self.client.login(username='gso_user', password='testpass123')
        
        request_ids = [str(req.id) for req in requests]
        response = self.client.post(reverse('supply:batch_operations'), {
            'action': 'bulk_approve',
            'selected_requests': request_ids,
        })
        self.assertEqual(response.status_code, 302)
        
        # Verify all requests were approved
        for request in requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'APPROVED')
        
        # Verify bulk audit log was created
        bulk_audit_logs = AuditLog.objects.filter(
            user=self.gso_user,
            action_type='BULK_OPERATION'
        )
        self.assertTrue(bulk_audit_logs.exists())

    def test_inventory_management_workflow(self):
        """Test inventory management workflow"""
        self.client.login(username='gso_user', password='testpass123')
        
        # View inventory
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.supply_item.name)
        
        # View item detail
        response = self.client.get(reverse('supply:inventory_detail', args=[self.supply_item.id]))
        self.assertEqual(response.status_code, 200)
        
        # Adjust stock
        initial_stock = self.supply_item.current_stock
        response = self.client.post(
            reverse('supply:inventory_adjust', args=[self.supply_item.id]),
            {
                'adjustment_type': 'IN',
                'quantity': 20,
                'remarks': 'Stock replenishment'
            }
        )
        self.assertEqual(response.status_code, 302)
        
        # Verify stock was adjusted
        self.supply_item.refresh_from_db()
        self.assertEqual(self.supply_item.current_stock, initial_stock + 20)
        
        # Verify transaction was created
        transaction = InventoryTransaction.objects.filter(
            item=self.supply_item,
            transaction_type='IN',
            quantity=20
        ).first()
        self.assertIsNotNone(transaction)

    def test_reporting_workflow(self):
        """Test reporting workflow"""
        # Create some test data
        self.client.login(username='dept_user', password='testpass123')
        
        # Create a request
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 5,
            'purpose': 'Report test request'
        })
        
        request = SupplyRequest.objects.filter(purpose='Report test request').first()
        
        # Approve and release as GSO
        self.client.login(username='gso_user', password='testpass123')
        
        request.approve(self.gso_user, 'Approved for report test')
        request.release(self.gso_user, 'Released for report test')
        
        # Test reports dashboard
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 200)
        
        # Test requests report
        response = self.client.get(reverse('supply:requests_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, request.request_id)
        
        # Test departmental usage report
        response = self.client.get(reverse('supply:departmental_usage_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'IT Department')
        
        # Test inventory report
        response = self.client.get(reverse('supply:inventory_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.supply_item.name)

    def test_search_functionality_workflow(self):
        """Test search functionality workflow"""
        # Create test data
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 5,
            'purpose': 'Searchable test request'
        })
        
        request = SupplyRequest.objects.filter(purpose='Searchable test request').first()
        
        # Test search as GSO user
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'searchable'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(len(data['results']) > 0)
        
        # Test search as department user
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.get(reverse('supply:global_search'), {
            'q': 'searchable'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(len(data['results']) > 0)

    def test_dashboard_widgets_workflow(self):
        """Test dashboard widgets workflow"""
        # Create test data
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.post(reverse('supply:request_create'), {
            'item': self.supply_item.id,
            'quantity': 5,
            'purpose': 'Widget test request'
        })
        
        # Test widgets as GSO user
        self.client.login(username='gso_user', password='testpass123')
        
        # Test statistics widget
        response = self.client.get(reverse('supply:dashboard_widgets'), {
            'widget': 'statistics'
        })
        self.assertEqual(response.status_code, 200)
        
        # Test pending approvals widget
        response = self.client.get(reverse('supply:dashboard_widgets'), {
            'widget': 'pending_approvals'
        })
        self.assertEqual(response.status_code, 200)
        
        # Test low stock alerts widget
        response = self.client.get(reverse('supply:dashboard_widgets'), {
            'widget': 'low_stock_alerts'
        })
        self.assertEqual(response.status_code, 200)

    def test_user_authentication_workflow(self):
        """Test user authentication and authorization workflow"""
        # Test login
        response = self.client.post(reverse('login'), {
            'username': 'dept_user',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        
        # Test access to department dashboard
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Test unauthorized access to GSO functions
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect due to role restriction
        
        # Test logout
        response = self.client.post(reverse('logout'))
        self.assertEqual(response.status_code, 302)
        
        # Test access after logout
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect to login
