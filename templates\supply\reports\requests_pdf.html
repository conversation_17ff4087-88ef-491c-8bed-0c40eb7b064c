<!DOCTYPE html>
<html>
<head>
    <title>Requests Report - {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .status-pending { color: #f59e0b; }
        .status-approved { color: #3b82f6; }
        .status-released { color: #10b981; }
        .status-rejected { color: #ef4444; }
    </style>
</head>
<body>
    <h1>Supply Requests Report</h1>
    <p><strong>Report Period:</strong> {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
    <p><strong>Generated on:</strong> {% now "M d, Y g:i A" %}</p>
    
    <table>
        <thead>
            <tr>
                <th>Request ID</th>
                <th>Department</th>
                <th>Requester</th>
                <th>Item</th>
                <th>Quantity</th>
                <th>Status</th>
                <th>Created Date</th>
            </tr>
        </thead>
        <tbody>
            {% for request in requests %}
            <tr>
                <td>{{ request.request_id }}</td>
                <td>{{ request.department }}</td>
                <td>{{ request.requester.get_full_name|default:request.requester.username }}</td>
                <td>{{ request.item.name }}</td>
                <td>{{ request.quantity }} {{ request.item.unit }}</td>
                <td class="status-{{ request.status|lower }}">{{ request.get_status_display }}</td>
                <td>{{ request.created_at|date:"M d, Y g:i A" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        <p>Municipal Supply Request and Release Management System</p>
        <p>This report contains {{ requests|length }} request(s)</p>
    </div>
</body>
</html>
