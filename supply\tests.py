from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction
from .forms import UserRegistrationForm, UserProfileForm, SupplyItemForm, StockAdjustmentForm, SupplyRequestForm, RequestFilterForm
from .decorators import role_required, is_gso_user, is_department_user


class UserProfileModelTest(TestCase):
    """Test UserProfile model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_user_profile_creation(self):
        """Test that UserProfile is created when User is created"""
        self.assertTrue(hasattr(self.user, 'userprofile'))
        self.assertIsInstance(self.user.userprofile, UserProfile)
    
    def test_user_profile_str_method(self):
        """Test UserProfile string representation"""
        profile = self.user.userprofile
        profile.role = 'GSO'
        profile.save()
        expected = f"{self.user.username} - GSO Staff"
        self.assertEqual(str(profile), expected)
    
    def test_role_choices(self):
        """Test that role choices are properly defined"""
        profile = self.user.userprofile
        profile.role = 'GSO'
        profile.save()
        self.assertEqual(profile.get_role_display(), 'GSO Staff')
        
        profile.role = 'DEPARTMENT'
        profile.save()
        self.assertEqual(profile.get_role_display(), 'Department Staff')


class UserRegistrationFormTest(TestCase):
    """Test user registration form"""
    
    def test_valid_registration_form(self):
        """Test form with valid data"""
        form_data = {
            'username': 'newuser',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'role': 'DEPARTMENT',
            'department': 'IT Department',
            'phone': '************',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_password_mismatch(self):
        """Test form with mismatched passwords"""
        form_data = {
            'username': 'newuser',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'role': 'DEPARTMENT',
            'department': 'IT Department',
            'password1': 'complexpass123',
            'password2': 'differentpass123'
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
    
    def test_form_save_creates_profile(self):
        """Test that form save creates user and profile"""
        form_data = {
            'username': 'newuser',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'role': 'GSO',
            'department': 'General Services',
            'phone': '************',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        user = form.save()
        self.assertEqual(user.username, 'newuser')
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Doe')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.userprofile.role, 'GSO')
        self.assertEqual(user.userprofile.department, 'General Services')
        self.assertEqual(user.userprofile.phone, '************')


class UserProfileFormTest(TestCase):
    """Test user profile form"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.user.userprofile.role = 'DEPARTMENT'
        self.user.userprofile.department = 'IT'
        self.user.userprofile.save()
    
    def test_profile_form_initialization(self):
        """Test form initialization with user data"""
        form = UserProfileForm(instance=self.user.userprofile, user=self.user)
        self.assertEqual(form.fields['first_name'].initial, 'Test')
        self.assertEqual(form.fields['last_name'].initial, 'User')
        self.assertEqual(form.fields['email'].initial, '<EMAIL>')
    
    def test_profile_form_save(self):
        """Test form save updates both user and profile"""
        form_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'email': '<EMAIL>',
            'department': 'HR Department',
            'phone': '************'
        }
        form = UserProfileForm(
            data=form_data, 
            instance=self.user.userprofile, 
            user=self.user
        )
        self.assertTrue(form.is_valid())
        
        form.save()
        self.user.refresh_from_db()
        
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.userprofile.department, 'HR Department')
        self.assertEqual(self.user.userprofile.phone, '************')


class AuthenticationViewsTest(TestCase):
    """Test authentication views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.userprofile.role = 'DEPARTMENT'
        self.user.userprofile.department = 'IT'
        self.user.userprofile.save()
    
    def test_register_view_get(self):
        """Test registration view GET request"""
        response = self.client.get(reverse('register'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create your account')
    
    def test_register_view_post_valid(self):
        """Test registration view with valid POST data"""
        form_data = {
            'username': 'newuser',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'role': 'GSO',
            'department': 'General Services',
            'phone': '************',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        }
        response = self.client.post(reverse('register'), data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful registration
        
        # Check user was created
        user = User.objects.get(username='newuser')
        self.assertEqual(user.userprofile.role, 'GSO')
    
    def test_register_redirect_if_authenticated(self):
        """Test that authenticated users are redirected from register page"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('register'))
        self.assertEqual(response.status_code, 302)
    
    def test_profile_view_requires_login(self):
        """Test that profile view requires authentication"""
        response = self.client.get(reverse('supply:profile'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_profile_view_authenticated(self):
        """Test profile view for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('supply:profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'User Profile')
    
    def test_profile_update(self):
        """Test profile update functionality"""
        self.client.login(username='testuser', password='testpass123')
        form_data = {
            'first_name': 'Updated',
            'last_name': 'User',
            'email': '<EMAIL>',
            'department': 'Updated Department',
            'phone': '555-1234'
        }
        response = self.client.post(reverse('supply:profile'), data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful update
        
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.userprofile.department, 'Updated Department')


class RoleBasedAccessTest(TestCase):
    """Test role-based access control"""
    
    def setUp(self):
        self.client = Client()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            password='testpass123'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT'
        self.dept_user.userprofile.save()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
    
    def test_gso_dashboard_requires_gso_role(self):
        """Test that GSO dashboard requires GSO role"""
        # Test department user access
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect due to insufficient permissions
        
        # Test GSO user access
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
    
    def test_inventory_requires_gso_role(self):
        """Test that inventory view requires GSO role"""
        # Test department user access
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 302)  # Redirect due to insufficient permissions
        
        # Test GSO user access
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 200)
    
    def test_reports_requires_gso_role(self):
        """Test that reports view requires GSO role"""
        # Test department user access
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 302)  # Redirect due to insufficient permissions
        
        # Test GSO user access
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 200)
    
    def test_dashboard_accessible_to_all_authenticated(self):
        """Test that main dashboard is accessible to all authenticated users"""
        # Test department user access
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Test GSO user access
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        self.assertEqual(response.status_code, 200)


class DecoratorTest(TestCase):
    """Test custom decorators"""
    
    def setUp(self):
        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            password='testpass123'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.save()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.save()
    
    def test_is_gso_user_helper(self):
        """Test is_gso_user helper function"""
        self.assertTrue(is_gso_user(self.gso_user))
        self.assertFalse(is_gso_user(self.dept_user))
    
    def test_is_department_user_helper(self):
        """Test is_department_user helper function"""
        self.assertTrue(is_department_user(self.dept_user))
        self.assertFalse(is_department_user(self.gso_user))
    
    def test_role_required_decorator_functionality(self):
        """Test role_required decorator logic"""
        # This would typically be tested with a mock view
        # For now, we test the helper functions that the decorator uses
        self.assertTrue(hasattr(self.gso_user, 'userprofile'))
        self.assertEqual(self.gso_user.userprofile.role, 'GSO')
        self.assertTrue(hasattr(self.dept_user, 'userprofile'))
        self.assertEqual(self.dept_user.userprofile.role, 'DEPARTMENT')


class MessageTest(TestCase):
    """Test message functionality in views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.user.userprofile.role = 'DEPARTMENT'
        self.user.userprofile.save()
    
    def test_registration_success_message(self):
        """Test success message on registration"""
        form_data = {
            'username': 'newuser',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'role': 'DEPARTMENT',
            'department': 'IT',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        }
        response = self.client.post(reverse('register'), data=form_data, follow=True)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('Registration successful', str(messages[0]))
    
    def test_profile_update_success_message(self):
        """Test success message on profile update"""
        self.client.login(username='testuser', password='testpass123')
        form_data = {
            'first_name': 'Updated',
            'last_name': 'User',
            'email': '<EMAIL>',
            'department': 'Updated Department',
            'phone': '555-1234'
        }
        response = self.client.post(reverse('supply:profile'), data=form_data, follow=True)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('Profile updated successfully', str(messages[0]))


class SupplyItemModelTest(TestCase):
    """Test SupplyItem model functionality"""
    
    def setUp(self):
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test description',
            unit='pieces',
            current_stock=50,
            minimum_stock=10
        )
    
    def test_supply_item_creation(self):
        """Test SupplyItem creation"""
        self.assertEqual(self.item.name, 'Test Item')
        self.assertEqual(self.item.current_stock, 50)
        self.assertEqual(self.item.minimum_stock, 10)
        self.assertEqual(self.item.unit, 'pieces')
    
    def test_supply_item_str_method(self):
        """Test SupplyItem string representation"""
        expected = "Test Item (50 pieces)"
        self.assertEqual(str(self.item), expected)
    
    def test_is_low_stock_property(self):
        """Test is_low_stock property"""
        # Item with stock above minimum
        self.assertFalse(self.item.is_low_stock)
        
        # Item with stock at minimum
        self.item.current_stock = 10
        self.item.save()
        self.assertTrue(self.item.is_low_stock)
        
        # Item with stock below minimum
        self.item.current_stock = 5
        self.item.save()
        self.assertTrue(self.item.is_low_stock)
    
    def test_can_fulfill_quantity_method(self):
        """Test can_fulfill_quantity method"""
        self.assertTrue(self.item.can_fulfill_quantity(30))
        self.assertTrue(self.item.can_fulfill_quantity(50))
        self.assertFalse(self.item.can_fulfill_quantity(60))
    
    def test_negative_stock_validation(self):
        """Test that negative stock values are not allowed"""
        self.item.current_stock = -5
        with self.assertRaises(ValidationError):
            self.item.full_clean()


class SupplyRequestModelTest(TestCase):
    """Test SupplyRequest model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.user.userprofile.role = 'DEPARTMENT'
        self.user.userprofile.department = 'IT'
        self.user.userprofile.save()
        
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.save()
        
        self.item = SupplyItem.objects.create(
            name='Test Item',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.request = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=20,
            purpose='Testing purposes'
        )
    
    def test_supply_request_creation(self):
        """Test SupplyRequest creation"""
        self.assertEqual(self.request.requester, self.user)
        self.assertEqual(self.request.item, self.item)
        self.assertEqual(self.request.quantity, 20)
        self.assertEqual(self.request.status, 'PENDING')
        self.assertEqual(self.request.department, 'IT')  # Auto-set from user profile
        self.assertTrue(self.request.request_id.startswith('REQ-'))
    
    def test_request_id_generation(self):
        """Test automatic request ID generation"""
        request2 = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=10,
            purpose='Another test'
        )
        self.assertNotEqual(self.request.request_id, request2.request_id)
        self.assertTrue(request2.request_id.startswith('REQ-'))
    
    def test_approve_method(self):
        """Test request approval method"""
        success = self.request.approve(self.gso_user, "Approved for testing")
        self.assertTrue(success)
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, 'APPROVED')
        self.assertEqual(self.request.approved_by, self.gso_user)
        self.assertEqual(self.request.approval_remarks, "Approved for testing")
        self.assertIsNotNone(self.request.approved_at)
    
    def test_approve_insufficient_stock(self):
        """Test approval fails with insufficient stock"""
        self.item.current_stock = 10
        self.item.save()
        
        success = self.request.approve(self.gso_user, "Should fail")
        self.assertFalse(success)
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, 'PENDING')
    
    def test_reject_method(self):
        """Test request rejection method"""
        success = self.request.reject(self.gso_user, "Not needed")
        self.assertTrue(success)
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, 'REJECTED')
        self.assertEqual(self.request.approved_by, self.gso_user)
        self.assertEqual(self.request.approval_remarks, "Not needed")
    
    def test_release_method(self):
        """Test request release method"""
        # First approve the request
        self.request.approve(self.gso_user, "Approved")
        
        # Then release it
        initial_stock = self.item.current_stock
        success = self.request.release(self.gso_user, "Released for testing")
        
        self.assertTrue(success)
        self.request.refresh_from_db()
        self.item.refresh_from_db()
        
        self.assertEqual(self.request.status, 'RELEASED')
        self.assertEqual(self.request.released_by, self.gso_user)
        self.assertEqual(self.request.release_remarks, "Released for testing")
        self.assertEqual(self.item.current_stock, initial_stock - self.request.quantity)
        
        # Check that inventory transaction was created
        transaction = InventoryTransaction.objects.filter(
            item=self.item,
            reference_request=self.request
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.transaction_type, 'OUT')
        self.assertEqual(transaction.quantity, self.request.quantity)


class InventoryTransactionModelTest(TestCase):
    """Test InventoryTransaction model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.item = SupplyItem.objects.create(
            name='Test Item',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.transaction = InventoryTransaction.objects.create(
            item=self.item,
            transaction_type='IN',
            quantity=50,
            performed_by=self.user,
            remarks='Initial stock'
        )
    
    def test_inventory_transaction_creation(self):
        """Test InventoryTransaction creation"""
        self.assertEqual(self.transaction.item, self.item)
        self.assertEqual(self.transaction.transaction_type, 'IN')
        self.assertEqual(self.transaction.quantity, 50)
        self.assertEqual(self.transaction.performed_by, self.user)
        self.assertEqual(self.transaction.remarks, 'Initial stock')
    
    def test_inventory_transaction_str_method(self):
        """Test InventoryTransaction string representation"""
        expected = "Stock In - Test Item (50)"
        self.assertEqual(str(self.transaction), expected)


class SupplyItemFormTest(TestCase):
    """Test SupplyItemForm functionality"""
    
    def test_valid_supply_item_form(self):
        """Test form with valid data"""
        form_data = {
            'name': 'New Item',
            'description': 'Test description',
            'unit': 'boxes',
            'current_stock': 25,
            'minimum_stock': 5
        }
        form = SupplyItemForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_supply_item_form_save(self):
        """Test form save creates SupplyItem"""
        form_data = {
            'name': 'New Item',
            'description': 'Test description',
            'unit': 'boxes',
            'current_stock': 25,
            'minimum_stock': 5
        }
        form = SupplyItemForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        item = form.save()
        self.assertEqual(item.name, 'New Item')
        self.assertEqual(item.current_stock, 25)
        self.assertEqual(item.minimum_stock, 5)


class StockAdjustmentFormTest(TestCase):
    """Test StockAdjustmentForm functionality"""
    
    def setUp(self):
        self.item = SupplyItem.objects.create(
            name='Test Item',
            unit='pieces',
            current_stock=50,
            minimum_stock=10
        )
    
    def test_valid_stock_adjustment_form(self):
        """Test form with valid data"""
        form_data = {
            'adjustment_type': 'IN',
            'quantity': 20,
            'remarks': 'Adding more stock'
        }
        form = StockAdjustmentForm(data=form_data, item=self.item)
        self.assertTrue(form.is_valid())
    
    def test_stock_adjustment_out_validation(self):
        """Test validation for OUT adjustment with insufficient stock"""
        form_data = {
            'adjustment_type': 'OUT',
            'quantity': 60,  # More than current stock of 50
            'remarks': 'Removing stock'
        }
        form = StockAdjustmentForm(data=form_data, item=self.item)
        self.assertFalse(form.is_valid())
        self.assertIn('Cannot remove 60 items', str(form.errors))


class InventoryViewsTest(TestCase):
    """Test inventory management views"""
    
    def setUp(self):
        self.client = Client()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            password='testpass123'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.save()
        
        # Create test items
        self.item1 = SupplyItem.objects.create(
            name='Test Item 1',
            unit='pieces',
            current_stock=50,
            minimum_stock=10
        )
        
        self.item2 = SupplyItem.objects.create(
            name='Low Stock Item',
            unit='boxes',
            current_stock=5,
            minimum_stock=10
        )
    
    def test_inventory_list_view_gso_access(self):
        """Test inventory list view for GSO users"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Item 1')
        self.assertContains(response, 'Low Stock Item')
        self.assertContains(response, '1 item running low')  # Low stock alert
    
    def test_inventory_list_view_department_denied(self):
        """Test inventory list view denies department users"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'))
        self.assertEqual(response.status_code, 302)  # Redirect due to insufficient permissions
    
    def test_inventory_search_functionality(self):
        """Test inventory search functionality"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'), {'search': 'Low Stock'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Low Stock Item')
        self.assertNotContains(response, 'Test Item 1')
    
    def test_inventory_low_stock_filter(self):
        """Test inventory low stock filter"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory'), {'low_stock': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Low Stock Item')
        self.assertNotContains(response, 'Test Item 1')
    
    def test_inventory_add_view_get(self):
        """Test inventory add view GET request"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory_add'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add New Supply Item')
    
    def test_inventory_add_view_post(self):
        """Test inventory add view POST request"""
        self.client.login(username='gsouser', password='testpass123')
        form_data = {
            'name': 'New Test Item',
            'description': 'Test description',
            'unit': 'pieces',
            'current_stock': 30,
            'minimum_stock': 5
        }
        response = self.client.post(reverse('supply:inventory_add'), data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        
        # Check item was created
        item = SupplyItem.objects.get(name='New Test Item')
        self.assertEqual(item.current_stock, 30)
        
        # Check initial transaction was created
        transaction = InventoryTransaction.objects.filter(
            item=item,
            transaction_type='IN'
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.quantity, 30)
    
    def test_inventory_edit_view(self):
        """Test inventory edit view"""
        self.client.login(username='gsouser', password='testpass123')
        
        # GET request
        response = self.client.get(reverse('supply:inventory_edit', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Test Item 1')
        
        # POST request
        form_data = {
            'name': 'Updated Item Name',
            'description': 'Updated description',
            'unit': 'pieces',
            'current_stock': 60,  # Changed from 50
            'minimum_stock': 15
        }
        response = self.client.post(
            reverse('supply:inventory_edit', args=[self.item1.id]), 
            data=form_data
        )
        self.assertEqual(response.status_code, 302)
        
        # Check item was updated
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.name, 'Updated Item Name')
        self.assertEqual(self.item1.current_stock, 60)
        
        # Check adjustment transaction was created (only if stock actually changed)
        transactions = InventoryTransaction.objects.filter(
            item=self.item1,
            transaction_type='ADJUSTMENT'
        )
        # Since we changed stock from 50 to 60, there should be a transaction
        self.assertEqual(transactions.count(), 1)
        transaction = transactions.first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.quantity, 10)  # abs(60 - 50)
    
    def test_inventory_detail_view(self):
        """Test inventory detail view"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory_detail', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Item 1')
        self.assertContains(response, '50 pieces')
    
    def test_inventory_adjust_view(self):
        """Test inventory adjust view"""
        self.client.login(username='gsouser', password='testpass123')
        
        # GET request
        response = self.client.get(reverse('supply:inventory_adjust', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Adjust Stock - Test Item 1')
        
        # POST request - add stock
        form_data = {
            'adjustment_type': 'IN',
            'quantity': 25,
            'remarks': 'Adding more stock for testing'
        }
        response = self.client.post(
            reverse('supply:inventory_adjust', args=[self.item1.id]), 
            data=form_data
        )
        self.assertEqual(response.status_code, 302)
        
        # Check stock was updated
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.current_stock, 75)  # 50 + 25
        
        # Check transaction was created
        transaction = InventoryTransaction.objects.filter(
            item=self.item1,
            transaction_type='IN',
            quantity=25
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.remarks, 'Adding more stock for testing')
    
    def test_inventory_delete_view(self):
        """Test inventory delete view"""
        self.client.login(username='gsouser', password='testpass123')
        
        # GET request
        response = self.client.get(reverse('supply:inventory_delete', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Delete Supply Item')
        
        # POST request
        response = self.client.post(reverse('supply:inventory_delete', args=[self.item1.id]))
        self.assertEqual(response.status_code, 302)
        
        # Check item was deleted
        with self.assertRaises(SupplyItem.DoesNotExist):
            SupplyItem.objects.get(id=self.item1.id)
    
    def test_low_stock_alerts_view(self):
        """Test low stock alerts view"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:low_stock_alerts'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Low Stock Alerts')
        self.assertContains(response, 'Low Stock Item')  # Should show low stock item
        self.assertNotContains(response, 'Test Item 1')  # Should not show normal stock item
    
    def test_inventory_transactions_view(self):
        """Test inventory transactions view"""
        # Create a transaction
        InventoryTransaction.objects.create(
            item=self.item1,
            transaction_type='IN',
            quantity=20,
            performed_by=self.gso_user,
            remarks='Test transaction'
        )
        
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:inventory_transactions'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Inventory Transactions')
        self.assertContains(response, 'Test transaction')
    
    def test_inventory_transactions_filtering(self):
        """Test inventory transactions filtering"""
        # Create transactions
        InventoryTransaction.objects.create(
            item=self.item1,
            transaction_type='IN',
            quantity=20,
            performed_by=self.gso_user,
            remarks='IN transaction'
        )
        InventoryTransaction.objects.create(
            item=self.item1,
            transaction_type='OUT',
            quantity=10,
            performed_by=self.gso_user,
            remarks='OUT transaction'
        )
        
        self.client.login(username='gsouser', password='testpass123')
        
        # Filter by item
        response = self.client.get(
            reverse('supply:inventory_transactions'), 
            {'item': self.item1.id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'IN transaction')
        self.assertContains(response, 'OUT transaction')
        
        # Filter by transaction type
        response = self.client.get(
            reverse('supply:inventory_transactions'), 
            {'type': 'IN'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'IN transaction')
        self.assertNotContains(response, 'OUT transaction')


class SupplyRequestFormTest(TestCase):
    """Test SupplyRequestForm functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.user.userprofile.role = 'DEPARTMENT'
        self.user.userprofile.department = 'IT'
        self.user.userprofile.save()
        
        self.item = SupplyItem.objects.create(
            name='Test Item',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.out_of_stock_item = SupplyItem.objects.create(
            name='Out of Stock Item',
            unit='boxes',
            current_stock=0,
            minimum_stock=5
        )
    
    def test_valid_supply_request_form(self):
        """Test form with valid data"""
        form_data = {
            'item': self.item.id,
            'quantity': 20,
            'purpose': 'This is a valid purpose for testing the form functionality'
        }
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())
    
    def test_form_only_shows_available_items(self):
        """Test that form only shows items with stock > 0"""
        form = SupplyRequestForm(user=self.user)
        available_items = form.fields['item'].queryset
        self.assertIn(self.item, available_items)
        self.assertNotIn(self.out_of_stock_item, available_items)
    
    def test_quantity_validation_positive(self):
        """Test quantity must be positive"""
        form_data = {
            'item': self.item.id,
            'quantity': 0,
            'purpose': 'This is a valid purpose for testing'
        }
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('Ensure this value is greater than or equal to 1', str(form.errors))
        
        form_data['quantity'] = -5
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('Ensure this value is greater than or equal to 1', str(form.errors))
    
    def test_quantity_validation_exceeds_stock(self):
        """Test quantity cannot exceed available stock"""
        form_data = {
            'item': self.item.id,
            'quantity': 150,  # More than current stock of 100
            'purpose': 'This is a valid purpose for testing'
        }
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('Requested quantity (150) exceeds available stock (100)', str(form.errors))
    
    def test_purpose_validation_minimum_length(self):
        """Test purpose must be at least 10 characters"""
        form_data = {
            'item': self.item.id,
            'quantity': 20,
            'purpose': 'Short'  # Less than 10 characters
        }
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('Purpose must be at least 10 characters long', str(form.errors))
    
    def test_form_save_sets_requester(self):
        """Test that form save sets the requester"""
        form_data = {
            'item': self.item.id,
            'quantity': 20,
            'purpose': 'This is a valid purpose for testing the form save functionality'
        }
        form = SupplyRequestForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())
        
        request = form.save()
        self.assertEqual(request.requester, self.user)
        self.assertEqual(request.department, 'IT')  # Auto-set from user profile
        self.assertTrue(request.request_id.startswith('REQ-'))


class RequestFilterFormTest(TestCase):
    """Test RequestFilterForm functionality"""
    
    def test_filter_form_initialization(self):
        """Test filter form initialization"""
        form = RequestFilterForm()
        self.assertIn(('', 'All Statuses'), form.fields['status'].choices)
        self.assertIn(('PENDING', 'Pending'), form.fields['status'].choices)
        self.assertIn(('APPROVED', 'Approved'), form.fields['status'].choices)
        self.assertIn(('REJECTED', 'Rejected'), form.fields['status'].choices)
        self.assertIn(('RELEASED', 'Released'), form.fields['status'].choices)
    
    def test_filter_form_with_initial_data(self):
        """Test filter form with initial data"""
        form = RequestFilterForm(initial={
            'status': 'PENDING',
            'search': 'test query'
        })
        self.assertEqual(form.fields['status'].initial, 'PENDING')
        self.assertEqual(form.fields['search'].initial, 'test query')


class SupplyRequestViewsTest(TestCase):
    """Test supply request views"""
    
    def setUp(self):
        self.client = Client()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            password='testpass123',
            first_name='John',
            last_name='Doe'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123',
            first_name='Jane',
            last_name='Smith'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.department = 'General Services'
        self.gso_user.userprofile.save()
        
        # Create test items
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item description',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.low_stock_item = SupplyItem.objects.create(
            name='Low Stock Item',
            unit='boxes',
            current_stock=5,
            minimum_stock=10
        )
        
        # Create test requests
        self.request1 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item,
            quantity=20,
            purpose='Testing purposes for the first request'
        )
        
        self.request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.low_stock_item,
            quantity=3,
            purpose='Testing purposes for the second request',
            status='APPROVED',
            approved_by=self.gso_user,
            approval_remarks='Approved for testing'
        )
    
    def test_request_create_view_get(self):
        """Test request create view GET request"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Supply Request')
        self.assertContains(response, 'Test Item')
        self.assertNotContains(response, 'Out of Stock')  # Only items with stock should show
    
    def test_request_create_view_post_valid(self):
        """Test request create view with valid POST data"""
        self.client.login(username='deptuser', password='testpass123')
        form_data = {
            'item': self.item.id,
            'quantity': 25,
            'purpose': 'This is a valid purpose for creating a new supply request'
        }
        response = self.client.post(reverse('supply:request_create'), data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        
        # Check request was created
        request = SupplyRequest.objects.filter(
            requester=self.dept_user,
            item=self.item,
            quantity=25
        ).first()
        self.assertIsNotNone(request)
        self.assertEqual(request.status, 'PENDING')
        self.assertEqual(request.department, 'IT Department')
    
    def test_request_create_view_post_invalid(self):
        """Test request create view with invalid POST data"""
        self.client.login(username='deptuser', password='testpass123')
        form_data = {
            'item': self.item.id,
            'quantity': 150,  # Exceeds available stock
            'purpose': 'Short'  # Too short
        }
        response = self.client.post(reverse('supply:request_create'), data=form_data)
        self.assertEqual(response.status_code, 200)  # Stay on form page
        self.assertContains(response, 'exceeds available stock')
        self.assertContains(response, 'at least 10 characters')
    
    def test_request_create_htmx_success(self):
        """Test request create with HTMX returns success template"""
        self.client.login(username='deptuser', password='testpass123')
        form_data = {
            'item': self.item.id,
            'quantity': 25,
            'purpose': 'This is a valid purpose for HTMX testing'
        }
        response = self.client.post(
            reverse('supply:request_create'), 
            data=form_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Request Submitted Successfully')
        self.assertContains(response, 'REQ-')  # Should contain request ID
    
    def test_request_history_view_department_user(self):
        """Test request history view for department users"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'My Supply Requests')
        self.assertContains(response, self.request1.request_id)
        self.assertContains(response, self.request2.request_id)
        self.assertContains(response, 'New Request')  # Should show create button
    
    def test_request_history_view_gso_user(self):
        """Test request history view for GSO users"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'All Supply Requests')
        self.assertContains(response, self.request1.request_id)
        self.assertContains(response, self.request2.request_id)
        self.assertNotContains(response, 'New Request')  # GSO shouldn't see create button
    
    def test_request_history_filtering_by_status(self):
        """Test request history filtering by status"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'), {'status': 'PENDING'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request1.request_id)
        self.assertNotContains(response, self.request2.request_id)
    
    def test_request_history_search_functionality(self):
        """Test request history search functionality"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'), {'search': 'Low Stock'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request2.request_id)
        self.assertNotContains(response, self.request1.request_id)
    
    def test_request_history_htmx_filtering(self):
        """Test request history HTMX filtering"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(
            reverse('supply:request_history'), 
            {'status': 'APPROVED'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request2.request_id)
        self.assertNotContains(response, self.request1.request_id)
        # Should return partial template
        self.assertNotContains(response, 'My Supply Requests')  # Header not in partial
    
    def test_request_detail_view_own_request(self):
        """Test request detail view for user's own request"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_detail', args=[self.request1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request1.request_id)
        self.assertContains(response, 'Test Item')
        self.assertContains(response, '20 pieces')
        self.assertContains(response, 'Testing purposes')
        self.assertContains(response, 'Pending Review')
    
    def test_request_detail_view_gso_access(self):
        """Test request detail view for GSO users"""
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:request_detail', args=[self.request1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request1.request_id)
    
    def test_request_detail_view_unauthorized_access(self):
        """Test request detail view denies unauthorized access"""
        # Create another department user
        other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )
        other_user.userprofile.role = 'DEPARTMENT'
        other_user.userprofile.save()
        
        self.client.login(username='otheruser', password='testpass123')
        response = self.client.get(reverse('supply:request_detail', args=[self.request1.id]))
        self.assertEqual(response.status_code, 302)  # Redirect due to insufficient permissions
    
    def test_request_detail_view_approved_request(self):
        """Test request detail view for approved request"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_detail', args=[self.request2.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Approved')
        self.assertContains(response, 'Approval Details')
        self.assertContains(response, 'Jane Smith')  # Approved by
        self.assertContains(response, 'Approved for testing')  # Approval remarks
    
    def test_item_info_htmx_endpoint(self):
        """Test item info HTMX endpoint"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(
            reverse('supply:item_info'),
            {'item': self.item.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Item')
        self.assertContains(response, '100 pieces')
        self.assertContains(response, 'Available Stock')
    
    def test_item_info_low_stock_warning(self):
        """Test item info shows low stock warning"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(
            reverse('supply:item_info'),
            {'item': self.low_stock_item.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Low Stock Alert')
        self.assertContains(response, 'below minimum stock level')
    
    def test_item_info_invalid_item(self):
        """Test item info with invalid item ID"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(
            reverse('supply:item_info'),
            {'item': 99999},  # Non-existent item
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode().strip(), '')  # Empty response
    
    def test_request_status_update_htmx(self):
        """Test request status update HTMX endpoint"""
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(
            reverse('supply:request_status_update'),
            {'request_ids': f'{self.request1.id},{self.request2.id}'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should contain JavaScript for updating status badges
        self.assertContains(response, 'statusElement')
        self.assertContains(response, f'request-{self.request1.id}')
        self.assertContains(response, f'request-{self.request2.id}')
    
    def test_request_requires_authentication(self):
        """Test that request views require authentication"""
        # Test create view
        response = self.client.get(reverse('supply:request_create'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test history view
        response = self.client.get(reverse('supply:request_history'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test detail view
        response = self.client.get(reverse('supply:request_detail', args=[self.request1.id]))
        self.assertEqual(response.status_code, 302)  # Redirect to login


class SupplyRequestIntegrationTest(TestCase):
    """Integration tests for supply request workflow"""
    
    def setUp(self):
        self.client = Client()
        
        # Create users
        self.dept_user = User.objects.create_user(
            username='deptuser',
            password='testpass123',
            first_name='John',
            last_name='Doe'
        )
        self.dept_user.userprofile.role = 'DEPARTMENT'
        self.dept_user.userprofile.department = 'IT Department'
        self.dept_user.userprofile.save()
        
        self.gso_user = User.objects.create_user(
            username='gsouser',
            password='testpass123'
        )
        self.gso_user.userprofile.role = 'GSO'
        self.gso_user.userprofile.save()
        
        # Create test item
        self.item = SupplyItem.objects.create(
            name='Integration Test Item',
            unit='pieces',
            current_stock=50,
            minimum_stock=10
        )
    
    def test_complete_request_workflow(self):
        """Test complete request workflow from creation to release"""
        # Step 1: Department user creates request
        self.client.login(username='deptuser', password='testpass123')
        form_data = {
            'item': self.item.id,
            'quantity': 20,
            'purpose': 'Integration testing of the complete workflow process'
        }
        response = self.client.post(reverse('supply:request_create'), data=form_data)
        self.assertEqual(response.status_code, 302)
        
        # Verify request was created
        request = SupplyRequest.objects.filter(
            requester=self.dept_user,
            item=self.item
        ).first()
        self.assertIsNotNone(request)
        self.assertEqual(request.status, 'PENDING')
        
        # Step 2: GSO user can see the request
        self.client.login(username='gsouser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'))
        self.assertContains(response, request.request_id)
        
        # Step 3: GSO user views request details
        response = self.client.get(reverse('supply:request_detail', args=[request.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Integration Test Item')
        self.assertContains(response, 'John Doe')
        
        # Step 4: Verify request appears in department user's history
        self.client.login(username='deptuser', password='testpass123')
        response = self.client.get(reverse('supply:request_history'))
        self.assertContains(response, request.request_id)
        self.assertContains(response, 'Pending')
    
    def test_request_validation_edge_cases(self):
        """Test request validation edge cases"""
        self.client.login(username='deptuser', password='testpass123')
        
        # Test requesting exact available stock
        form_data = {
            'item': self.item.id,
            'quantity': 50,  # Exact current stock
            'purpose': 'Testing exact stock quantity validation'
        }
        response = self.client.post(reverse('supply:request_create'), data=form_data)
        self.assertEqual(response.status_code, 302)  # Should succeed
        
        # Verify request was created
        request = SupplyRequest.objects.filter(quantity=50).first()
        self.assertIsNotNone(request)
    
    def test_concurrent_request_handling(self):
        """Test handling of concurrent requests for same item"""
        self.client.login(username='deptuser', password='testpass123')
        
        # Create first request
        form_data1 = {
            'item': self.item.id,
            'quantity': 30,
            'purpose': 'First concurrent request for testing purposes'
        }
        response1 = self.client.post(reverse('supply:request_create'), data=form_data1)
        self.assertEqual(response1.status_code, 302)
        
        # Create second request (should still be allowed at request time)
        form_data2 = {
            'item': self.item.id,
            'quantity': 25,
            'purpose': 'Second concurrent request for testing purposes'
        }
        response2 = self.client.post(reverse('supply:request_create'), data=form_data2)
        self.assertEqual(response2.status_code, 302)
        
        # Both requests should be created
        requests = SupplyRequest.objects.filter(item=self.item)
        self.assertEqual(requests.count(), 2)
    
    def test_request_form_dynamic_validation(self):
        """Test dynamic form validation with changing stock levels"""
        # Reduce item stock
        self.item.current_stock = 10
        self.item.save()
        
        self.client.login(username='deptuser', password='testpass123')
        
        # Try to request more than available
        form_data = {
            'item': self.item.id,
            'quantity': 15,  # More than current stock of 10
            'purpose': 'Testing dynamic validation with reduced stock'
        }
        response = self.client.post(reverse('supply:request_create'), data=form_data)
        self.assertEqual(response.status_code, 200)  # Stay on form
        self.assertContains(response, 'exceeds available stock')


class SupplyRequestModelValidationTest(TestCase):
    """Test SupplyRequest model validation"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.user.userprofile.department = 'Test Department'
        self.user.userprofile.save()
        
        self.item = SupplyItem.objects.create(
            name='Test Item',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
    
    def test_request_id_uniqueness(self):
        """Test that request IDs are unique"""
        request1 = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=10,
            purpose='First request for uniqueness testing'
        )
        
        request2 = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=15,
            purpose='Second request for uniqueness testing'
        )
        
        self.assertNotEqual(request1.request_id, request2.request_id)
        self.assertTrue(request1.request_id.startswith('REQ-'))
        self.assertTrue(request2.request_id.startswith('REQ-'))
    
    def test_department_auto_population(self):
        """Test that department is auto-populated from user profile"""
        request = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=10,
            purpose='Testing department auto-population functionality'
        )
        
        self.assertEqual(request.department, 'Test Department')
    
    def test_quantity_validation(self):
        """Test quantity validation at model level"""
        # Test zero quantity
        with self.assertRaises(ValidationError):
            request = SupplyRequest(
                requester=self.user,
                item=self.item,
                quantity=0,
                purpose='Testing zero quantity validation'
            )
            request.full_clean()
        
        # Test negative quantity
        with self.assertRaises(ValidationError):
            request = SupplyRequest(
                requester=self.user,
                item=self.item,
                quantity=-5,
                purpose='Testing negative quantity validation'
            )
            request.full_clean()
    
    def test_status_workflow_validation(self):
        """Test status workflow validation"""
        request = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=10,
            purpose='Testing status workflow validation'
        )
        
        # Test initial status
        self.assertEqual(request.status, 'PENDING')
        
        # Test approve method
        gso_user = User.objects.create_user(username='gso', password='test')
        gso_user.userprofile.role = 'GSO'
        gso_user.userprofile.save()
        
        success = request.approve(gso_user, 'Approved for testing')
        self.assertTrue(success)
        self.assertEqual(request.status, 'APPROVED')
        
        # Test that already approved request cannot be approved again
        success = request.approve(gso_user, 'Try to approve again')
        self.assertFalse(success)  # Should fail
    
    def test_request_string_representation(self):
        """Test request string representation"""
        request = SupplyRequest.objects.create(
            requester=self.user,
            item=self.item,
            quantity=25,
            purpose='Testing string representation functionality'
        )
        
        expected = f"{request.request_id} - Test Item (25)"
        self.assertEqual(str(request), expected)


class GSODashboardTestCase(TestCase):
    """Test cases for GSO dashboard functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='GSO',
            last_name='Staff'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='Department',
            last_name='Staff'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply items
        self.item1 = SupplyItem.objects.create(
            name='Office Paper',
            description='A4 white paper',
            unit='reams',
            current_stock=100,
            minimum_stock=10
        )
        
        self.item2 = SupplyItem.objects.create(
            name='Ballpoint Pens',
            description='Blue ink pens',
            unit='pieces',
            current_stock=5,  # Low stock
            minimum_stock=20
        )
        
        # Create supply requests
        self.pending_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item1,
            quantity=10,
            purpose='For monthly reports and documentation',
            status='PENDING'
        )
        
        self.approved_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item1,
            quantity=5,
            purpose='For office use',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now(),
            approval_remarks='Approved for office use'
        )
        
        self.rejected_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item2,
            quantity=50,  # More than available stock
            purpose='For distribution',
            status='REJECTED',
            approved_by=self.gso_user,
            approved_at=timezone.now(),
            approval_remarks='Insufficient stock available'
        )
        
        self.client = Client()
    
    def test_gso_dashboard_access_authenticated_gso(self):
        """Test that GSO users can access the dashboard"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GSO Dashboard')
        self.assertContains(response, 'Manage supply requests and approvals')
    
    def test_gso_dashboard_access_denied_department_user(self):
        """Test that department users cannot access GSO dashboard"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        # Should redirect or show access denied
        self.assertNotEqual(response.status_code, 200)
    
    def test_gso_dashboard_access_denied_anonymous(self):
        """Test that anonymous users cannot access GSO dashboard"""
        response = self.client.get(reverse('supply:gso_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_gso_dashboard_displays_statistics(self):
        """Test that dashboard displays correct statistics"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        
        # Check statistics are displayed
        self.assertContains(response, 'Total Requests')
        self.assertContains(response, 'Pending')
        self.assertContains(response, 'Approved')
        self.assertContains(response, 'Rejected')
        
        # Check correct counts
        context = response.context
        self.assertEqual(context['total_requests'], 3)
        self.assertEqual(context['pending_requests'], 1)
        self.assertEqual(context['approved_requests'], 1)
        self.assertEqual(context['rejected_requests'], 1)
    
    def test_gso_dashboard_filtering_by_status(self):
        """Test filtering requests by status"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test pending filter
        response = self.client.get(reverse('supply:gso_dashboard'), {'status': 'PENDING'})
        self.assertEqual(response.status_code, 200)
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 1)
        self.assertEqual(requests[0].status, 'PENDING')
        
        # Test approved filter
        response = self.client.get(reverse('supply:gso_dashboard'), {'status': 'APPROVED'})
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 1)
        self.assertEqual(requests[0].status, 'APPROVED')
    
    def test_gso_dashboard_filtering_by_department(self):
        """Test filtering requests by department"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Clear status filter to see all requests
        response = self.client.get(reverse('supply:gso_dashboard'), {'department': 'IT Department', 'status': ''})
        self.assertEqual(response.status_code, 200)
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 3)  # All requests are from IT Department
        
        # Test non-existent department
        response = self.client.get(reverse('supply:gso_dashboard'), {'department': 'HR Department', 'status': ''})
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 0)
    
    def test_gso_dashboard_search_functionality(self):
        """Test search functionality"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Search by item name (clear status filter to see all requests)
        response = self.client.get(reverse('supply:gso_dashboard'), {'search': 'Office Paper', 'status': ''})
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 2)  # Two requests for Office Paper
        
        # Search by purpose
        response = self.client.get(reverse('supply:gso_dashboard'), {'search': 'monthly reports', 'status': ''})
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 1)
        
        # Search by request ID
        response = self.client.get(reverse('supply:gso_dashboard'), {'search': self.pending_request.request_id, 'status': ''})
        requests = response.context['page_obj'].object_list
        self.assertEqual(len(requests), 1)
        self.assertEqual(requests[0].id, self.pending_request.id)
    
    def test_gso_dashboard_sorting(self):
        """Test sorting functionality"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test sorting by creation date (newest first - default)
        response = self.client.get(reverse('supply:gso_dashboard'), {'sort': '-created_at'})
        requests = list(response.context['page_obj'].object_list)
        self.assertTrue(requests[0].created_at >= requests[-1].created_at)
        
        # Test sorting by status
        response = self.client.get(reverse('supply:gso_dashboard'), {'sort': 'status'})
        requests = list(response.context['page_obj'].object_list)
        statuses = [req.status for req in requests]
        self.assertEqual(statuses, sorted(statuses))
    
    def test_gso_dashboard_htmx_partial_update(self):
        """Test HTMX partial page updates"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Make HTMX request
        response = self.client.get(
            reverse('supply:gso_dashboard'),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should return partial template
        self.assertTemplateUsed(response, 'supply/gso/dashboard_list.html')
    
    def test_gso_dashboard_pagination(self):
        """Test pagination functionality"""
        # Create more requests to test pagination
        for i in range(20):
            SupplyRequest.objects.create(
                requester=self.dept_user,
                department='IT Department',
                item=self.item1,
                quantity=1,
                purpose=f'Test request {i}',
                status='PENDING'
            )
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        
        # Check pagination
        self.assertTrue(response.context['page_obj'].has_other_pages())
        self.assertEqual(len(response.context['page_obj'].object_list), 15)  # Default page size
        
        # Test second page
        response = self.client.get(reverse('supply:gso_dashboard'), {'page': 2})
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.context['page_obj'].object_list) > 0)


class GSORequestDetailTestCase(TestCase):
    """Test cases for GSO request detail functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create users
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Office Paper',
            description='A4 white paper',
            unit='reams',
            current_stock=100,
            minimum_stock=10
        )
        
        # Create supply request
        self.request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=10,
            purpose='For monthly reports and documentation',
            status='PENDING'
        )
        
        self.client = Client()
    
    def test_gso_request_detail_access(self):
        """Test GSO can access request detail"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_request_detail', args=[self.request.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Request Details')
        self.assertContains(response, self.request.request_id)
    
    def test_gso_request_detail_displays_correct_info(self):
        """Test request detail displays correct information"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_request_detail', args=[self.request.id]))
        
        # Check request information
        self.assertContains(response, self.request.request_id)
        self.assertContains(response, self.request.item.name)
        self.assertContains(response, str(self.request.quantity))
        self.assertContains(response, self.request.purpose)
        self.assertContains(response, self.request.department)
        self.assertContains(response, self.request.requester.get_full_name())
    
    def test_gso_request_detail_shows_correct_actions(self):
        """Test request detail shows correct actions based on status"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Pending request should show approve/reject actions
        response = self.client.get(reverse('supply:gso_request_detail', args=[self.request.id]))
        context = response.context
        self.assertTrue(context['can_approve'])
        self.assertFalse(context['can_release'])
        self.assertTrue(context['inventory_available'])
        
        # Approved request should show release action
        self.request.status = 'APPROVED'
        self.request.approved_by = self.gso_user
        self.request.approved_at = timezone.now()
        self.request.save()
        
        response = self.client.get(reverse('supply:gso_request_detail', args=[self.request.id]))
        context = response.context
        self.assertFalse(context['can_approve'])
        self.assertTrue(context['can_release'])


class GSORequestActionsTestCase(TestCase):
    """Test cases for GSO request actions (approve, reject, release)"""
    
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Office Paper',
            description='A4 white paper',
            unit='reams',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()
    
    def test_approve_request_success(self):
        """Test successful request approval"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=10,
            purpose='For office use',
            status='PENDING'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Approved for office use'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Check request was approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'APPROVED')
        self.assertEqual(request.approved_by, self.gso_user)
        self.assertEqual(request.approval_remarks, 'Approved for office use')
        self.assertIsNotNone(request.approved_at)
    
    def test_approve_request_insufficient_stock(self):
        """Test approval fails with insufficient stock"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=200,  # More than available stock
            purpose='For office use',
            status='PENDING'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.post(
            reverse('supply:approve_request', args=[request.id]),
            {'approval_remarks': 'Trying to approve'}
        )
        
        # Check request was not approved
        request.refresh_from_db()
        self.assertEqual(request.status, 'PENDING')
        self.assertIsNone(request.approved_by)
    
    def test_reject_request_success(self):
        """Test successful request rejection"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=10,
            purpose='For office use',
            status='PENDING'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': 'Not needed at this time'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Check request was rejected
        request.refresh_from_db()
        self.assertEqual(request.status, 'REJECTED')
        self.assertEqual(request.approved_by, self.gso_user)
        self.assertEqual(request.approval_remarks, 'Not needed at this time')
        self.assertIsNotNone(request.approved_at)
    
    def test_reject_request_requires_remarks(self):
        """Test rejection requires remarks"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=10,
            purpose='For office use',
            status='PENDING'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.post(
            reverse('supply:reject_request', args=[request.id]),
            {'rejection_remarks': ''}  # Empty remarks
        )
        
        # Check request was not rejected
        request.refresh_from_db()
        self.assertEqual(request.status, 'PENDING')
    
    def test_release_request_success(self):
        """Test successful request release"""
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.item,
            quantity=10,
            purpose='For office use',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )
        
        initial_stock = self.item.current_stock
        
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.post(
            reverse('supply:release_request', args=[request.id]),
            {'release_remarks': 'Released to IT Department'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Check request was released
        request.refresh_from_db()
        self.assertEqual(request.status, 'RELEASED')
        self.assertEqual(request.released_by, self.gso_user)
        self.assertEqual(request.release_remarks, 'Released to IT Department')
        self.assertIsNotNone(request.released_at)
        
        # Check inventory was updated
        self.item.refresh_from_db()
        self.assertEqual(self.item.current_stock, initial_stock - request.quantity)
        
        # Check inventory transaction was created
        transaction = InventoryTransaction.objects.filter(
            item=self.item,
            reference_request=request,
            transaction_type='OUT'
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.quantity, request.quantity)


class GSOBatchOperationsTestCase(TestCase):
    """Test cases for GSO batch operations"""
    
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Office Paper',
            description='A4 white paper',
            unit='reams',
            current_stock=100,
            minimum_stock=10
        )
        
        # Create multiple pending requests
        self.requests = []
        for i in range(3):
            request = SupplyRequest.objects.create(
                requester=self.dept_user,
                department='IT Department',
                item=self.item,
                quantity=5,
                purpose=f'Test request {i}',
                status='PENDING'
            )
            self.requests.append(request)
        
        self.client = Client()
    
    def test_bulk_approve_success(self):
        """Test successful bulk approval"""
        self.client.login(username='gso_user', password='testpass123')
        
        request_ids = [str(req.id) for req in self.requests]
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_approve',
                'selected_requests': request_ids
            }
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Check all requests were approved
        for request in self.requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'APPROVED')
            self.assertEqual(request.approved_by, self.gso_user)
    
    def test_bulk_reject_success(self):
        """Test successful bulk rejection"""
        self.client.login(username='gso_user', password='testpass123')
        
        request_ids = [str(req.id) for req in self.requests]
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_reject',
                'selected_requests': request_ids,
                'rejection_reason': 'Bulk rejection for testing'
            }
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Check all requests were rejected
        for request in self.requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'REJECTED')
            self.assertEqual(request.approved_by, self.gso_user)
            self.assertEqual(request.approval_remarks, 'Bulk rejection for testing')
    
    def test_batch_operations_no_requests_selected(self):
        """Test batch operations with no requests selected"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_approve',
                'selected_requests': []
            }
        )
        
        # Should redirect with error message
        self.assertEqual(response.status_code, 302)
        
        # Check no requests were modified
        for request in self.requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'PENDING')
    
    def test_batch_operations_access_denied_department_user(self):
        """Test batch operations access denied for department users"""
        self.client.login(username='dept_user', password='testpass123')
        
        request_ids = [str(req.id) for req in self.requests]
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_approve',
                'selected_requests': request_ids
            }
        )
        
        # Should be denied access
        self.assertNotEqual(response.status_code, 200)