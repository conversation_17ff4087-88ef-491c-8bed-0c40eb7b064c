#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest
from django.contrib.auth.models import User

def test_batch_request():
    print("=== Simple Batch Request Test ===")
    
    # Get batch request
    batch_req = SupplyRequest.objects.filter(is_batch_request=True, status='PENDING').first()
    if not batch_req:
        print("No pending batch requests found")
        return
    
    print(f"Testing batch request: {batch_req.request_id}")
    print(f"Status: {batch_req.status}")
    print(f"Items: {batch_req.request_items.count()}")
    
    # Check context variables that the view should provide
    inventory_available = False
    item_missing = False
    
    if batch_req.is_batch_request:
        if batch_req.request_items.exists():
            inventory_available = all(
                item.item.can_fulfill_quantity(item.quantity) 
                for item in batch_req.request_items.all()
            )
        else:
            item_missing = True
    
    can_approve = batch_req.status == 'PENDING' and not item_missing
    can_release = batch_req.status == 'APPROVED' and not item_missing
    
    print(f"\nContext variables:")
    print(f"  inventory_available: {inventory_available}")
    print(f"  item_missing: {item_missing}")
    print(f"  can_approve: {can_approve}")
    print(f"  can_release: {can_release}")
    
    if can_approve:
        print("\n✓ Batch request should show approval interface")
    else:
        print("\n✗ Batch request cannot be approved")
    
    print(f"\nBatch request URL: /gso/batch-requests/{batch_req.id}/")
    print("You can now test the approval interface in the browser")

if __name__ == '__main__':
    test_batch_request()
