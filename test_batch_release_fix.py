#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest
from django.contrib.auth.models import User

def test_batch_release_fix():
    print("=== Testing Batch Release Fix ===")
    
    # Get approved batch requests
    batch_requests = SupplyRequest.objects.filter(is_batch_request=True, status='APPROVED')
    print(f"\nApproved batch requests: {batch_requests.count()}")
    
    for req in batch_requests:
        print(f"\n--- Request: {req.request_id} ---")
        print(f"Status: {req.status}")
        print(f"Items: {req.request_items.count()}")
        
        # Test the new methods
        can_release = req.can_be_released()
        stock_status = req.get_stock_status()
        
        print(f"Can be released: {can_release}")
        print(f"Stock status: {stock_status}")
        
        if req.request_items.exists():
            print("Item details:")
            for item in req.request_items.all():
                approved_qty = item.approved_quantity or item.quantity
                available = item.item.can_fulfill_quantity(approved_qty)
                print(f"  - {item.item.name}: {approved_qty} requested, {item.item.current_stock} available ({'✓' if available else '✗'})")
        
        print(f"Expected result: {'Ready to Release' if can_release else 'Insufficient Stock'}")
    
    # Test single item requests too
    single_requests = SupplyRequest.objects.filter(is_batch_request=False, status='APPROVED')
    print(f"\n=== Single Item Requests ===")
    print(f"Approved single requests: {single_requests.count()}")
    
    for req in single_requests[:2]:  # Test first 2
        print(f"\n--- Request: {req.request_id} ---")
        print(f"Item: {req.item.name if req.item else 'None'}")
        print(f"Quantity: {req.quantity}")
        
        can_release = req.can_be_released()
        stock_status = req.get_stock_status()
        
        print(f"Can be released: {can_release}")
        print(f"Stock status: {stock_status}")
        
        if req.item:
            print(f"Available stock: {req.item.current_stock}")
            print(f"Expected result: {'Ready to Release' if can_release else 'Insufficient Stock'}")
    
    print(f"\n=== Summary ===")
    print("The release management page should now correctly show:")
    print("- 'Ready to Release' for batch requests with sufficient stock for ALL items")
    print("- 'Insufficient Stock' for batch requests with insufficient stock for ANY item")
    print("- Proper item counts and stock status messages")
    print("- Correct links to batch request detail pages")
    
    print(f"\nTest the release management page at: http://127.0.0.1:8000/gso/releases/")

if __name__ == '__main__':
    test_batch_release_fix()
