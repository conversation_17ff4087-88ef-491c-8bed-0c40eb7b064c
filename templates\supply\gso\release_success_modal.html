<!-- Release Success Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Success Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full mb-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>
        
        <!-- Title -->
        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">
            Release Successful
        </h3>
        
        <!-- Message -->
        <p class="text-sm text-gray-600 text-center mb-6">
            {{ success_message|default:"Supplies have been released successfully." }}
        </p>
        
        <!-- Action Buttons -->
        <div class="flex justify-center space-x-3">
            <button type="button"
                    @click="show = false; setTimeout(() => { window.location.href = '{% url 'supply:release_management' %}'; }, 300)"
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                Back to Release Management
            </button>
        </div>
    </div>
</div>

<!-- Auto-redirect after 3 seconds -->
<script>
    setTimeout(() => {
        window.location.href = '{% url 'supply:release_management' %}';
    }, 3000);
</script>
