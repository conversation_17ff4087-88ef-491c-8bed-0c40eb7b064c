{% extends 'base_new.html' %}

{% block title %}Request History - MSRRMS{% endblock %}

{% block page_title %}My Requests{% endblock %}
{% block mobile_title %}My Requests{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">
                        {% if user.userprofile.role == 'GSO' %}
                            All Supply Requests
                        {% else %}
                            My Supply Requests
                        {% endif %}
                    </h1>
                    <p class="text-blue-100 mt-1">
                        {% if user.userprofile.role == 'GSO' %}
                            View and manage all supply requests from departments
                        {% else %}
                            Track the status of your submitted supply requests
                        {% endif %}
                    </p>
                </div>
            </div>
            {% if user.userprofile.role != 'GSO' %}
            <div class="hidden md:block">
                <a href="{% url 'supply:request_create' %}"
                   class="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-lg text-white hover:bg-white hover:text-blue-600 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    New Request
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Statistics Cards -->
    {% if user.userprofile.role != 'GSO' %}
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <!-- Pending Requests -->
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-yellow-400">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="h-4 w-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Pending</p>
                    <p class="text-lg font-semibold text-gray-900">{{ pending_count|default:0 }}</p>
                </div>
            </div>
        </div>

        <!-- Approved Requests -->
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-400">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Approved</p>
                    <p class="text-lg font-semibold text-gray-900">{{ approved_count|default:0 }}</p>
                </div>
            </div>
        </div>

        <!-- Released Requests -->
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-400">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Released</p>
                    <p class="text-lg font-semibold text-gray-900">{{ released_count|default:0 }}</p>
                </div>
            </div>
        </div>

        <!-- Total Requests -->
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-gray-400">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total</p>
                    <p class="text-lg font-semibold text-gray-900">{{ total_count|default:0 }}</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="px-6 py-5">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Filter Requests</h3>
                <div class="flex items-center space-x-2">
                    <div id="loading-indicator" class="htmx-indicator">
                        <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search Field -->
                <div class="space-y-2">
                    <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Search
                    </label>
                    <input type="text"
                           name="search"
                           id="{{ filter_form.search.id_for_label }}"
                           value="{{ filter_form.search.value|default:'' }}"
                           placeholder="Search by request ID, item name..."
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                </div>

                <!-- Status Filter -->
                <div class="space-y-2">
                    <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Status
                    </label>
                    <select name="status"
                            id="{{ filter_form.status.id_for_label }}"
                            class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        <option value="">All Statuses</option>
                        {% for choice in filter_form.status.field.choices %}
                            {% if choice.0 %}
                                <option value="{{ choice.0 }}" {% if choice.0 == filter_form.status.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">&nbsp;</label>
                    <button type="submit"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Requests List -->
    <div id="requests-list">
        {% include 'supply/requests/history_list.html' %}
    </div>
</div>

<!-- Auto-refresh for real-time updates -->
<div hx-get="{% url 'supply:request_status_update' %}?request_ids={% for request in page_obj %}{{ request.id }}{% if not forloop.last %},{% endif %}{% endfor %}" 
     hx-trigger="every 30s" 
     hx-target="#requests-list" 
     hx-swap="outerHTML">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up HTMX attributes for filter form
    const searchInput = document.getElementById('{{ filter_form.search.id_for_label }}');
    const statusSelect = document.getElementById('{{ filter_form.status.id_for_label }}');
    
    if (searchInput) {
        searchInput.setAttribute('hx-get', '{% url "supply:request_history" %}');
        searchInput.setAttribute('hx-target', '#requests-list');
        searchInput.setAttribute('hx-trigger', 'keyup changed delay:500ms');
        searchInput.setAttribute('hx-include', '[name="status"]');
    }
    
    if (statusSelect) {
        statusSelect.setAttribute('hx-get', '{% url "supply:request_history" %}');
        statusSelect.setAttribute('hx-target', '#requests-list');
        statusSelect.setAttribute('hx-trigger', 'change');
        statusSelect.setAttribute('hx-include', '[name="search"]');
    }
});
</script>
{% endblock %}