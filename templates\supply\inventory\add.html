{% extends 'base_new.html' %}

{% block title %}Add Supply Item{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
            <a href="{% url 'supply:inventory' %}" 
               class="text-blue-600 hover:text-blue-800 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Add New Supply Item</h1>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Item Name *
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.description.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Unit of Measurement *
                    </label>
                    {{ form.unit }}
                    {% if form.unit.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.unit.errors.0 }}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">e.g., pieces, boxes, reams, liters</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.current_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Current Stock *
                        </label>
                        {{ form.current_stock }}
                        {% if form.current_stock.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.current_stock.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.minimum_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Stock Level *
                        </label>
                        {{ form.minimum_stock }}
                        {% if form.minimum_stock.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.minimum_stock.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Alert when stock falls below this level</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6">
                    <a href="{% url 'supply:inventory' %}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Item
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}