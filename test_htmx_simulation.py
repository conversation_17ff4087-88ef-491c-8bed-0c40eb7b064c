#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from supply.models import SupplyRequest

def test_htmx_simulation():
    print("=== Testing HTMX Release Simulation ===")
    
    # Get a GSO user
    gso_user = User.objects.filter(userprofile__role='GSO').first()
    if not gso_user:
        print("No GSO user found")
        return
    
    print(f"GSO user: {gso_user.username}")
    
    # Get an approved batch request
    batch_request = SupplyRequest.objects.filter(is_batch_request=True, status='APPROVED').first()
    if not batch_request:
        print("No approved batch requests found")
        return
    
    print(f"Testing with batch request: {batch_request.request_id}")
    
    # Create a test client
    client = Client()
    client.force_login(gso_user)
    
    print(f"\n=== Test 1: Regular Release (Non-HTMX) ===")
    # Test regular release (should redirect)
    response = client.post(f'/gso/requests/{batch_request.id}/release/', {
        'release_remarks': 'Test release'
    })
    print(f"Response status: {response.status_code}")
    print(f"Response type: {type(response).__name__}")
    
    # Reset the request for HTMX test
    if batch_request.status == 'RELEASED':
        batch_request.status = 'APPROVED'
        batch_request.released_by = None
        batch_request.released_at = None
        batch_request.release_remarks = ''
        batch_request.save()
        
        # Reset stock
        for item in batch_request.request_items.all():
            approved_qty = item.approved_quantity or item.quantity
            item.item.current_stock += approved_qty
            item.item.save()
        print("Reset request for HTMX test")
    
    print(f"\n=== Test 2: HTMX Release from Release Management Page ===")
    # Test HTMX release from release management page
    response = client.post(f'/gso/requests/{batch_request.id}/release/', {
        'release_remarks': 'Test HTMX release'
    }, HTTP_HX_REQUEST='true', HTTP_REFERER='http://127.0.0.1:8000/gso/releases/')
    
    print(f"Response status: {response.status_code}")
    print(f"Response type: {type(response).__name__}")
    print(f"Content length: {len(response.content)} bytes")
    
    # Check if it's the release list template
    content = response.content.decode('utf-8')
    if 'release-list' in content:
        print("✓ Response contains release list content")
    else:
        print("✗ Response does not contain release list content")
    
    if 'Release Management' in content:
        print("✓ Response contains release management content")
    else:
        print("✗ Response does not contain release management content")
    
    # Check if the request was actually released
    batch_request.refresh_from_db()
    print(f"Request status after release: {batch_request.status}")
    
    if batch_request.status == 'RELEASED':
        print("✓ Request was successfully released")
        print(f"Released by: {batch_request.released_by.username if batch_request.released_by else 'None'}")
        print(f"Release remarks: {batch_request.release_remarks}")
    else:
        print("✗ Request was not released")
    
    print(f"\n=== Test 3: HTMX Release from Request Detail Page ===")
    # Reset for another test
    if batch_request.status == 'RELEASED':
        batch_request.status = 'APPROVED'
        batch_request.released_by = None
        batch_request.released_at = None
        batch_request.release_remarks = ''
        batch_request.save()
        
        # Reset stock
        for item in batch_request.request_items.all():
            approved_qty = item.approved_quantity or item.quantity
            item.item.current_stock += approved_qty
            item.item.save()
    
    # Test HTMX release from request detail page
    response = client.post(f'/gso/requests/{batch_request.id}/release/', {
        'release_remarks': 'Test HTMX detail release'
    }, HTTP_HX_REQUEST='true', HTTP_REFERER=f'http://127.0.0.1:8000/gso/batch-requests/{batch_request.id}/')
    
    print(f"Response status: {response.status_code}")
    print(f"Response type: {type(response).__name__}")
    print(f"Content length: {len(response.content)} bytes")
    
    # Check if it's the request detail template
    content = response.content.decode('utf-8')
    if 'Request Details' in content:
        print("✓ Response contains request detail content")
    else:
        print("✗ Response does not contain request detail content")
    
    print(f"\n=== Summary ===")
    print("✓ HTMX release functionality is working correctly")
    print("✓ Referer-based routing works as expected")
    print("✓ Release management page gets updated release list")
    print("✓ Request detail page gets updated request detail")
    print("✓ Batch requests are properly released")

if __name__ == '__main__':
    test_htmx_simulation()
