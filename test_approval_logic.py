#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest, SupplyRequestItem
from django.contrib.auth.models import User
from django.utils import timezone

def test_approval_logic():
    print("=== Testing Batch Approval Logic ===")
    
    # Get a pending batch request
    batch_request = SupplyRequest.objects.filter(is_batch_request=True, status='PENDING').first()
    
    if not batch_request:
        print("No pending batch requests found")
        return
    
    print(f"\nTesting with request: {batch_request.request_id}")
    print(f"Current status: {batch_request.status}")
    print(f"Items count: {batch_request.request_items.count()}")
    
    # Get GSO user
    gso_user = User.objects.filter(userprofile__role='GSO').first()
    if not gso_user:
        print("No GSO user found")
        return
    
    print(f"GSO user: {gso_user.username}")
    
    # Test the approval logic manually
    print(f"\n=== Testing Approval Logic ===")
    
    # Simulate the approval process
    print("Before approval:")
    print(f"  Status: {batch_request.status}")
    print(f"  Approved by: {batch_request.approved_by}")
    print(f"  Approved at: {batch_request.approved_at}")
    print(f"  Approval remarks: {batch_request.approval_remarks}")
    
    # Simulate approve_all action
    remarks = "Test approval via script"
    
    # Apply the same logic as in the view
    batch_request.status = 'APPROVED'
    batch_request.approved_by = gso_user
    batch_request.approved_at = timezone.now()
    batch_request.approval_remarks = remarks
    batch_request.save()
    
    # Update all items in the batch
    for request_item in batch_request.request_items.all():
        request_item.approved_quantity = request_item.quantity
        request_item.remarks = remarks
        request_item.save()
    
    print("\nAfter approval:")
    print(f"  Status: {batch_request.status}")
    print(f"  Approved by: {batch_request.approved_by.username if batch_request.approved_by else None}")
    print(f"  Approved at: {batch_request.approved_at}")
    print(f"  Approval remarks: {batch_request.approval_remarks}")
    
    print("\nItem approval status:")
    for request_item in batch_request.request_items.all():
        print(f"  - {request_item.item.name}: {request_item.approved_quantity}/{request_item.quantity}")
    
    print("\n✓ Approval logic test completed successfully!")
    print("The batch request should now be approved and ready for release.")
    
    # Reset for testing (optional)
    reset = input("\nReset request to PENDING for further testing? (y/n): ").lower().strip()
    if reset == 'y':
        batch_request.status = 'PENDING'
        batch_request.approved_by = None
        batch_request.approved_at = None
        batch_request.approval_remarks = ''
        batch_request.save()
        
        for request_item in batch_request.request_items.all():
            request_item.approved_quantity = None
            request_item.remarks = ''
            request_item.save()
        
        print("✓ Request reset to PENDING status")

if __name__ == '__main__':
    test_approval_logic()
