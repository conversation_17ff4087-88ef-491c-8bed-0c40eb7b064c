from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction


class ReportsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply items
        self.supply_item1 = SupplyItem.objects.create(
            name='Test Item 1',
            description='First test item',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.supply_item2 = SupplyItem.objects.create(
            name='Test Item 2',
            description='Second test item',
            unit='boxes',
            current_stock=5,  # Low stock
            minimum_stock=10
        )
        
        # Create supply requests with different statuses
        self.pending_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item1,
            quantity=10,
            purpose='Testing pending request',
            status='PENDING'
        )
        
        self.approved_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item1,
            quantity=15,
            purpose='Testing approved request',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )
        
        self.released_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item1,
            quantity=20,
            purpose='Testing released request',
            status='RELEASED',
            approved_by=self.gso_user,
            approved_at=timezone.now(),
            released_by=self.gso_user,
            released_at=timezone.now()
        )
        
        self.rejected_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item1,
            quantity=5,
            purpose='Testing rejected request',
            status='REJECTED',
            approved_by=self.gso_user,
            approved_at=timezone.now(),
            approval_remarks='Insufficient justification'
        )
        
        # Create inventory transactions
        InventoryTransaction.objects.create(
            item=self.supply_item1,
            transaction_type='OUT',
            quantity=20,
            reference_request=self.released_request,
            performed_by=self.gso_user,
            remarks='Released for testing'
        )
        
        self.client = Client()

    def test_reports_dashboard_access(self):
        """Test access to reports dashboard"""
        # Test GSO user can access
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Reports Dashboard')
        
        # Test department user cannot access
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:reports'))
        self.assertEqual(response.status_code, 302)  # Redirected due to role restriction

    def test_reports_dashboard_statistics(self):
        """Test that dashboard shows correct statistics"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:reports'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check context data
        self.assertEqual(response.context['total_requests'], 4)
        self.assertEqual(response.context['pending_requests'], 1)
        self.assertEqual(response.context['approved_requests'], 1)
        self.assertEqual(response.context['released_requests'], 1)
        self.assertEqual(response.context['rejected_requests'], 1)

    def test_requests_report_access(self):
        """Test access to requests report"""
        # Test GSO user can access
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:requests_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Requests Report')
        
        # Test department user cannot access
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:requests_report'))
        self.assertEqual(response.status_code, 302)

    def test_requests_report_filtering(self):
        """Test filtering in requests report"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test department filter
        response = self.client.get(
            reverse('supply:requests_report'),
            {'department': 'IT Department'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['page_obj'].paginator.count, 4)
        
        # Test status filter
        response = self.client.get(
            reverse('supply:requests_report'),
            {'status': 'PENDING'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['page_obj'].paginator.count, 1)

    def test_requests_report_csv_export(self):
        """Test CSV export functionality"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:requests_report'),
            {'export': 'csv'}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
        
        # Check that CSV contains data
        content = response.content.decode('utf-8')
        self.assertIn('Request ID', content)
        self.assertIn(self.pending_request.request_id, content)

    def test_departmental_usage_report(self):
        """Test departmental usage report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:departmental_usage_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Departmental Usage Report')
        
        # Check that IT Department appears in statistics
        dept_stats = response.context['dept_stats']
        it_dept_stats = next((dept for dept in dept_stats if dept['department'] == 'IT Department'), None)
        self.assertIsNotNone(it_dept_stats)
        self.assertEqual(it_dept_stats['total_requests'], 4)

    def test_inventory_report(self):
        """Test inventory report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(reverse('supply:inventory_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Inventory Report')
        
        # Check summary statistics
        self.assertEqual(response.context['total_items'], 2)
        self.assertEqual(response.context['low_stock_count'], 1)  # supply_item2 is low stock

    def test_inventory_report_low_stock_filter(self):
        """Test low stock filter in inventory report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:inventory_report'),
            {'low_stock': 'true'}
        )
        
        self.assertEqual(response.status_code, 200)
        # Should only show items with low stock
        items = response.context['page_obj']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0].name, 'Test Item 2')

    def test_inventory_report_search(self):
        """Test search functionality in inventory report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:inventory_report'),
            {'search': 'Test Item 1'}
        )
        
        self.assertEqual(response.status_code, 200)
        items = response.context['page_obj']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0].name, 'Test Item 1')

    def test_reports_date_filtering(self):
        """Test date range filtering in reports"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test with specific date range
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        
        response = self.client.get(
            reverse('supply:reports'),
            {
                'start_date': yesterday.strftime('%Y-%m-%d'),
                'end_date': today.strftime('%Y-%m-%d')
            }
        )
        
        self.assertEqual(response.status_code, 200)
        # All requests should be included as they were created today
        self.assertEqual(response.context['total_requests'], 4)

    def test_export_permissions(self):
        """Test that only GSO users can export reports"""
        # Test department user cannot export
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(
            reverse('supply:requests_report'),
            {'export': 'csv'}
        )
        self.assertEqual(response.status_code, 302)  # Redirected due to role restriction

    def test_pdf_export_placeholder(self):
        """Test PDF export functionality (placeholder implementation)"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:requests_report'),
            {'export': 'pdf'}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertIn('attachment', response['Content-Disposition'])

    def test_departmental_usage_csv_export(self):
        """Test CSV export for departmental usage report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:departmental_usage_report'),
            {'export': 'csv'}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        
        content = response.content.decode('utf-8')
        self.assertIn('Department', content)
        self.assertIn('IT Department', content)

    def test_inventory_csv_export(self):
        """Test CSV export for inventory report"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.get(
            reverse('supply:inventory_report'),
            {'export': 'csv'}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        
        content = response.content.decode('utf-8')
        self.assertIn('Item Name', content)
        self.assertIn('Test Item 1', content)
        self.assertIn('Test Item 2', content)
