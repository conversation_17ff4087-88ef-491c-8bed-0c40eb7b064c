from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction


class ReleaseManagementTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for release testing',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        # Create approved supply request
        self.approved_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=10,
            purpose='Testing release functionality',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now(),
            approval_remarks='Approved for testing'
        )
        
        # Create pending supply request
        self.pending_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=5,
            purpose='Testing pending request',
            status='PENDING'
        )
        
        self.client = Client()

    def test_release_approved_request_success(self):
        """Test successful release of approved request"""
        self.client.login(username='gso_user', password='testpass123')
        
        initial_stock = self.supply_item.current_stock
        
        response = self.client.post(
            reverse('supply:release_request', args=[self.approved_request.id]),
            {'release_remarks': 'Released for testing'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Refresh objects from database
        self.approved_request.refresh_from_db()
        self.supply_item.refresh_from_db()
        
        # Check request status updated
        self.assertEqual(self.approved_request.status, 'RELEASED')
        self.assertEqual(self.approved_request.released_by, self.gso_user)
        self.assertIsNotNone(self.approved_request.released_at)
        self.assertEqual(self.approved_request.release_remarks, 'Released for testing')
        
        # Check inventory updated
        self.assertEqual(self.supply_item.current_stock, initial_stock - 10)
        
        # Check inventory transaction created
        transaction = InventoryTransaction.objects.filter(
            reference_request=self.approved_request,
            transaction_type='OUT'
        ).first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.quantity, 10)
        self.assertEqual(transaction.performed_by, self.gso_user)

    def test_release_pending_request_fails(self):
        """Test that pending requests cannot be released"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:release_request', args=[self.pending_request.id]),
            {'release_remarks': 'Should not work'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Refresh object from database
        self.pending_request.refresh_from_db()
        
        # Check request status unchanged
        self.assertEqual(self.pending_request.status, 'PENDING')
        self.assertIsNone(self.pending_request.released_by)
        self.assertIsNone(self.pending_request.released_at)

    def test_release_insufficient_stock_fails(self):
        """Test that release fails when insufficient stock"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Set stock to less than requested quantity
        self.supply_item.current_stock = 5
        self.supply_item.save()
        
        initial_stock = self.supply_item.current_stock
        
        response = self.client.post(
            reverse('supply:release_request', args=[self.approved_request.id]),
            {'release_remarks': 'Should fail due to insufficient stock'}
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Refresh objects from database
        self.approved_request.refresh_from_db()
        self.supply_item.refresh_from_db()
        
        # Check request status unchanged
        self.assertEqual(self.approved_request.status, 'APPROVED')
        self.assertIsNone(self.approved_request.released_by)
        self.assertIsNone(self.approved_request.released_at)
        
        # Check inventory unchanged
        self.assertEqual(self.supply_item.current_stock, initial_stock)

    def test_release_management_view_access(self):
        """Test access to release management view"""
        # Test GSO user can access
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Release Management')
        
        # Test department user cannot access
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 302)  # Redirected due to role restriction

    def test_release_management_filtering(self):
        """Test filtering in release management view"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test department filter
        response = self.client.get(
            reverse('supply:release_management'),
            {'department': 'IT Department'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.approved_request.request_id)
        
        # Test search filter
        response = self.client.get(
            reverse('supply:release_management'),
            {'search': 'Test Item'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.approved_request.request_id)

    def test_batch_release_operation(self):
        """Test batch release operation"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Create another approved request
        approved_request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=15,
            purpose='Second test request',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )
        
        initial_stock = self.supply_item.current_stock
        
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_release',
                'selected_requests': [self.approved_request.id, approved_request2.id],
                'release_notes': 'Bulk release test'
            }
        )
        
        # Check redirect
        self.assertEqual(response.status_code, 302)
        
        # Refresh objects from database
        self.approved_request.refresh_from_db()
        approved_request2.refresh_from_db()
        self.supply_item.refresh_from_db()
        
        # Check both requests released
        self.assertEqual(self.approved_request.status, 'RELEASED')
        self.assertEqual(approved_request2.status, 'RELEASED')
        
        # Check inventory updated correctly
        expected_stock = initial_stock - 10 - 15
        self.assertEqual(self.supply_item.current_stock, expected_stock)
        
        # Check transactions created
        transactions = InventoryTransaction.objects.filter(
            transaction_type='OUT',
            item=self.supply_item
        )
        self.assertEqual(transactions.count(), 2)

    def test_model_release_method(self):
        """Test the release method on SupplyRequest model"""
        initial_stock = self.supply_item.current_stock
        
        # Test successful release
        result = self.approved_request.release(self.gso_user, 'Model test release')
        self.assertTrue(result)
        
        # Refresh objects
        self.approved_request.refresh_from_db()
        self.supply_item.refresh_from_db()
        
        # Check status updated
        self.assertEqual(self.approved_request.status, 'RELEASED')
        self.assertEqual(self.approved_request.released_by, self.gso_user)
        self.assertIsNotNone(self.approved_request.released_at)
        self.assertEqual(self.approved_request.release_remarks, 'Model test release')
        
        # Check inventory updated
        self.assertEqual(self.supply_item.current_stock, initial_stock - 10)
        
        # Test release of already released request fails
        result = self.approved_request.release(self.gso_user, 'Should fail')
        self.assertFalse(result)

    def test_unauthorized_access_to_release(self):
        """Test that department users cannot access release functionality"""
        self.client.login(username='dept_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:release_request', args=[self.approved_request.id]),
            {'release_remarks': 'Unauthorized attempt'}
        )
        
        # Should be redirected due to role restriction
        self.assertEqual(response.status_code, 302)
        
        # Check request status unchanged
        self.approved_request.refresh_from_db()
        self.assertEqual(self.approved_request.status, 'APPROVED')
