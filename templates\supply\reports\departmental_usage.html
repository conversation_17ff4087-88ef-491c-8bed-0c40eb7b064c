{% extends 'base_new.html' %}

{% block title %}Departmental Usage Report - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Departmental Usage Report</h1>
                    <p class="mt-1 text-lg text-gray-600">Usage statistics by department</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'supply:reports' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Back to Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters and Export -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="{{ start_date|date:'Y-m-d' }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="{{ end_date|date:'Y-m-d' }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                        <select name="department" id="department" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                            <option value="{{ dept }}" {% if dept == department %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" 
                                class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Filter
                        </button>
                    </div>
                    <div class="flex items-end space-x-2">
                        <a href="?{{ request.GET.urlencode }}&export=csv" 
                           class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center text-sm">
                            Export CSV
                        </a>
                        <a href="?{{ request.GET.urlencode }}&export=pdf" 
                           class="flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-center text-sm">
                            Export PDF
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Report Summary</h3>
                <p class="text-sm text-gray-600">
                    Departmental usage from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                    {% if department %}for department "{{ department }}"{% endif %}
                </p>
            </div>
        </div>

        <!-- Departmental Statistics Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            {% if dept_stats %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total Requests
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pending
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Approved
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Released
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Rejected
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total Quantity Released
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Success Rate
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for dept in dept_stats %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ dept.department }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.total_requests }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.pending_requests }}</div>
                                {% if dept.pending_requests > 0 %}
                                <div class="text-xs text-yellow-600">
                                    {{ dept.pending_requests|floatformat:0 }}% of total
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.approved_requests }}</div>
                                {% if dept.approved_requests > 0 %}
                                <div class="text-xs text-blue-600">
                                    {% widthratio dept.approved_requests dept.total_requests 100 %}% of total
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.released_requests }}</div>
                                {% if dept.released_requests > 0 %}
                                <div class="text-xs text-green-600">
                                    {% widthratio dept.released_requests dept.total_requests 100 %}% of total
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.rejected_requests }}</div>
                                {% if dept.rejected_requests > 0 %}
                                <div class="text-xs text-red-600">
                                    {% widthratio dept.rejected_requests dept.total_requests 100 %}% of total
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ dept.total_quantity_released|default:0 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if dept.total_requests > 0 %}
                                    {% widthratio dept.released_requests dept.total_requests 100 as success_rate %}
                                    {% if success_rate >= 80 %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ success_rate }}%
                                        </span>
                                    {% elif success_rate >= 60 %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            {{ success_rate }}%
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {{ success_rate }}%
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-gray-400">N/A</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No departmental data found</h3>
                <p class="mt-1 text-sm text-gray-500">No departmental usage data matches the selected criteria.</p>
            </div>
            {% endif %}
        </div>

        <!-- Charts Section (Placeholder for future enhancement) -->
        {% if dept_stats %}
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Visual Analytics</h3>
                <div class="text-center py-8 text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2">Charts and visual analytics coming soon</p>
                    <p class="text-sm">Interactive charts will be available in a future update</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
