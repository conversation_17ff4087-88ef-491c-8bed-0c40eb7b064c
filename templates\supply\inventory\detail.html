{% extends 'base_new.html' %}

{% block title %}{{ item.name }} - Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <a href="{% url 'supply:inventory' %}" 
           class="text-blue-600 hover:text-blue-800 mr-4">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </a>
        <h1 class="text-3xl font-bold text-gray-900">{{ item.name }}</h1>
        {% if item.is_low_stock %}
        <span class="ml-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            Low Stock Alert
        </span>
        {% endif %}
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Item <PERSON> -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Item Information</h2>
                
                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.name }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Unit</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.unit }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Current Stock</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <span class="{% if item.is_low_stock %}text-red-600 font-semibold{% else %}text-gray-900{% endif %}">
                                {{ item.current_stock }} {{ item.unit }}
                            </span>
                        </dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Minimum Stock</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.minimum_stock }} {{ item.unit }}</dd>
                    </div>
                    
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.description|default:"No description provided" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.created_at|date:"M d, Y g:i A" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ item.updated_at|date:"M d, Y g:i A" }}</dd>
                    </div>
                </dl>
            </div>

            <!-- Transaction History -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Transaction History</h2>
                    <a href="{% url 'supply:inventory_transactions' %}?item={{ item.id }}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All Transactions
                    </a>
                </div>

                {% if page_obj %}
                <div class="flow-root">
                    <ul class="-mb-8">
                        {% for transaction in page_obj %}
                        <li>
                            <div class="relative pb-8">
                                {% if not forloop.last %}
                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                {% endif %}
                                <div class="relative flex space-x-3">
                                    <div>
                                        {% if transaction.transaction_type == 'IN' %}
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </span>
                                        {% elif transaction.transaction_type == 'OUT' %}
                                        <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
                                            </svg>
                                        </span>
                                        {% else %}
                                        <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </span>
                                        {% endif %}
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">
                                                <span class="font-medium text-gray-900">{{ transaction.get_transaction_type_display }}</span>
                                                of {{ transaction.quantity }} {{ item.unit }}
                                                {% if transaction.reference_request %}
                                                for request <a href="#" class="text-blue-600 hover:text-blue-800">{{ transaction.reference_request.request_id }}</a>
                                                {% endif %}
                                            </p>
                                            <p class="text-sm text-gray-500">
                                                By {{ transaction.performed_by.get_full_name|default:transaction.performed_by.username }}
                                            </p>
                                            {% if transaction.remarks %}
                                            <p class="text-sm text-gray-600 mt-1">{{ transaction.remarks }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ transaction.created_at|date:"M d, Y" }}
                                            <br>
                                            {{ transaction.created_at|time:"g:i A" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <div class="mt-6 flex justify-center">
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-6">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
                    <p class="mt-1 text-sm text-gray-500">No inventory transactions recorded for this item yet.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                
                <div class="space-y-3">
                    <a href="{% url 'supply:inventory_edit' item.id %}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Edit Item
                    </a>
                    
                    <a href="{% url 'supply:inventory_adjust' item.id %}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Adjust Stock
                    </a>
                    
                    <a href="{% url 'supply:inventory_delete' item.id %}" 
                       class="w-full flex justify-center py-2 px-4 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        Delete Item
                    </a>
                </div>
            </div>

            <!-- Stock Status -->
            <div class="bg-white shadow rounded-lg p-6 mt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Stock Status</h3>
                
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Current Stock</span>
                            <span class="font-medium">{{ item.current_stock }} {{ item.unit }}</span>
                        </div>
                        <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                            {% widthratio item.current_stock item.minimum_stock|add:item.current_stock 100 as stock_percentage %}
                            <div class="h-2 rounded-full {% if item.is_low_stock %}bg-red-600{% else %}bg-green-600{% endif %}" 
                                 style="width: {{ stock_percentage|default:0 }}%"></div>
                        </div>
                    </div>
                    
                    <div class="text-sm">
                        <span class="text-gray-500">Minimum Level:</span>
                        <span class="font-medium">{{ item.minimum_stock }} {{ item.unit }}</span>
                    </div>
                    
                    {% if item.is_low_stock %}
                    <div class="bg-red-50 border border-red-200 rounded-md p-3">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Low Stock Alert</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>This item is running low on stock. Consider restocking soon.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="bg-green-50 border border-green-200 rounded-md p-3">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Stock OK</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p>Stock levels are adequate.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}