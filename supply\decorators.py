from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse


def role_required(*roles):
    """
    Decorator to restrict access to users with specific roles.
    Usage: @role_required('GSO') or @role_required('GSO', 'DEPARTMENT')
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            if not hasattr(request.user, 'userprofile'):
                messages.error(request, 'User profile not found. Please contact administrator.')
                return redirect('supply:dashboard')
            
            user_role = request.user.userprofile.role
            if user_role not in roles:
                messages.error(request, 'You do not have permission to access this page.')
                return redirect('supply:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def gso_required(view_func):
    """
    Decorator to restrict access to GSO users only.
    Shortcut for @role_required('GSO')
    """
    return role_required('GSO')(view_func)


def department_required(view_func):
    """
    Decorator to restrict access to Department users only.
    Shortcut for @role_required('DEPARTMENT')
    """
    return role_required('DEPARTMENT')(view_func)


def user_passes_test_with_message(test_func, message="You do not have permission to access this page.", redirect_url=None):
    """
    Decorator for views that checks that the user passes the given test,
    redirecting to the specified URL with a message if necessary.
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            if not test_func(request.user):
                messages.error(request, message)
                if redirect_url:
                    return redirect(redirect_url)
                return redirect('supply:dashboard')
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def is_gso_user(user):
    """Helper function to check if user is GSO"""
    return hasattr(user, 'userprofile') and user.userprofile.role == 'GSO'


def is_department_user(user):
    """Helper function to check if user is Department staff"""
    return hasattr(user, 'userprofile') and user.userprofile.role == 'DEPARTMENT'


def same_department_required(view_func):
    """
    Decorator to ensure users can only access resources from their own department.
    This should be used in conjunction with views that handle department-specific data.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        # GSO users can access all departments
        if hasattr(request.user, 'userprofile') and request.user.userprofile.role == 'GSO':
            return view_func(request, *args, **kwargs)
        
        # For department users, additional checks would be implemented in the view
        # This decorator mainly serves as a reminder and can be extended as needed
        return view_func(request, *args, **kwargs)
    return _wrapped_view