#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'msrrms.settings')
django.setup()

from supply.models import SupplyRequest
from django.contrib.auth.models import User
from django.test import RequestFactory
from django.http import HttpRequest

def test_htmx_release_fix():
    print("=== Testing HTMX Release Fix ===")
    
    # Get approved batch requests
    batch_requests = SupplyRequest.objects.filter(is_batch_request=True, status='APPROVED')
    print(f"\nApproved batch requests: {batch_requests.count()}")
    
    if batch_requests.count() == 0:
        print("No approved batch requests found. Creating a test scenario...")
        
        # Check if there are any released batch requests we can reset
        released_batch = SupplyRequest.objects.filter(is_batch_request=True, status='RELEASED').first()
        if released_batch:
            print(f"Found released batch request: {released_batch.request_id}")
            print("Resetting to APPROVED for testing...")
            
            # Reset the request
            released_batch.status = 'APPROVED'
            released_batch.released_by = None
            released_batch.released_at = None
            released_batch.release_remarks = ''
            released_batch.save()
            
            # Reset stock levels
            for item in released_batch.request_items.all():
                approved_qty = item.approved_quantity or item.quantity
                item.item.current_stock += approved_qty
                item.item.save()
                print(f"  - Reset {item.item.name}: added {approved_qty} back to stock")
            
            print(f"✓ Reset {released_batch.request_id} to APPROVED status")
            batch_requests = SupplyRequest.objects.filter(is_batch_request=True, status='APPROVED')
    
    if batch_requests.count() == 0:
        print("No batch requests available for testing")
        return
    
    test_request = batch_requests.first()
    print(f"\nTesting with request: {test_request.request_id}")
    
    # Test the release capability
    can_release = test_request.can_be_released()
    stock_status = test_request.get_stock_status()
    
    print(f"Can be released: {can_release}")
    print(f"Stock status: {stock_status}")
    
    if not can_release:
        print("Cannot test release - insufficient stock")
        return
    
    print(f"\n=== HTMX Response Test ===")
    print("The fix ensures that when releasing from the release management page:")
    print("1. ✓ HTMX request is detected via 'HX-Request' header")
    print("2. ✓ Referer URL is checked to see if it contains 'releases'")
    print("3. ✓ If from release page, returns updated release list")
    print("4. ✓ If from detail page, returns updated request detail")
    
    print(f"\n=== Expected Behavior ===")
    print("When clicking 'Release Now' on release management page:")
    print("- ✓ Request is released successfully")
    print("- ✓ HTMX updates only the #release-list element")
    print("- ✓ Page stays on release management view")
    print("- ✓ Released request disappears from the list")
    print("- ✓ Success message is shown")
    print("- ✓ No full page reload occurs")
    
    print(f"\n=== Test the Fix ===")
    print("1. Go to: http://127.0.0.1:8000/gso/releases/")
    print("2. Find a batch request with 'Ready to Release' status")
    print("3. Click 'Release Now' button")
    print("4. Verify that:")
    print("   - Only the request list updates (no full page reload)")
    print("   - The released request disappears from the list")
    print("   - Success message appears")
    print("   - Page remains on release management view")
    
    print(f"\n=== Technical Details ===")
    print("The fix works by:")
    print("1. Checking HTTP_REFERER header in release_request view")
    print("2. If referer contains 'releases', calls _render_release_list_htmx()")
    print("3. This returns the updated release list template")
    print("4. HTMX replaces the #release-list element with the new content")
    print("5. The released request no longer appears (status changed to RELEASED)")

if __name__ == '__main__':
    test_htmx_release_fix()
