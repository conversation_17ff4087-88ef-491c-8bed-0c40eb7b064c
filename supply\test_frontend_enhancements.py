from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from .models import UserProfile, SupplyItem, SupplyRequest


class FrontendEnhancementsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create department user
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'IT Department'
        self.dept_profile.save()
        
        # Create supply item
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for frontend testing',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )
        
        self.client = Client()

    def test_base_template_includes_frontend_libraries(self):
        """Test that base template includes HTMX, Alpine.js, and Tailwind CSS"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for HTMX
        self.assertContains(response, 'htmx.org')
        
        # Check for Alpine.js
        self.assertContains(response, 'alpinejs')
        
        # Check for Tailwind CSS
        self.assertContains(response, 'tailwindcss.com')

    def test_global_notification_system_present(self):
        """Test that global notification system is present in base template"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for notification container
        self.assertContains(response, 'notification-container')
        
        # Check for global app initialization
        self.assertContains(response, 'x-data="globalApp()"')
        
        # Check for notification functions
        self.assertContains(response, 'addNotification')
        self.assertContains(response, 'removeNotification')

    def test_loading_indicators_present(self):
        """Test that loading indicators are present"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for global loading indicator
        self.assertContains(response, 'global-loading')
        self.assertContains(response, 'htmx-indicator')

    def test_request_create_form_enhancements(self):
        """Test that request creation form has Alpine.js enhancements"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:request_create'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for Alpine.js data binding
        self.assertContains(response, 'x-data="requestForm()"')
        
        # Check for loading states
        self.assertContains(response, 'isSubmitting')
        
        # Check for form validation
        self.assertContains(response, 'validateQuantity')

    def test_gso_dashboard_interactive_features(self):
        """Test that GSO dashboard has interactive features"""
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:gso_dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for batch operations
        self.assertContains(response, 'selectedRequests')
        self.assertContains(response, 'toggleRequest')
        
        # Check for HTMX integration
        self.assertContains(response, 'hx-get')
        self.assertContains(response, 'hx-target')

    def test_htmx_csrf_token_configuration(self):
        """Test that HTMX is configured with CSRF token"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for CSRF configuration
        self.assertContains(response, 'htmx:configRequest')
        self.assertContains(response, 'X-CSRFToken')

    def test_real_time_updates_configuration(self):
        """Test that real-time updates are configured"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:request_history'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for auto-refresh
        self.assertContains(response, 'hx-trigger="every 30s"')

    def test_responsive_design_classes(self):
        """Test that responsive design classes are present"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for responsive classes
        self.assertContains(response, 'sm:')
        self.assertContains(response, 'md:')
        self.assertContains(response, 'lg:')

    def test_transition_animations_present(self):
        """Test that transition animations are present"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for transition classes
        self.assertContains(response, 'transition')
        self.assertContains(response, 'x-transition')

    def test_form_validation_feedback(self):
        """Test that forms have validation feedback"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:request_create'))

        self.assertEqual(response.status_code, 200)

        # Check for validation elements
        self.assertContains(response, 'text-red-500')  # Error styling
        self.assertContains(response, 'quantity-warning')  # Warning class

    def test_item_info_htmx_integration(self):
        """Test that item info uses HTMX for dynamic loading"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Test the item info endpoint
        response = self.client.get(
            reverse('supply:item_info'),
            {'item': self.supply_item.id}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.supply_item.name)

    def test_batch_operations_with_notifications(self):
        """Test that batch operations integrate with notification system"""
        # Create a pending request
        request = SupplyRequest.objects.create(
            requester=self.dept_user,
            department='IT Department',
            item=self.supply_item,
            quantity=10,
            purpose='Testing batch operations',
            status='PENDING'
        )
        
        self.client.login(username='gso_user', password='testpass123')
        
        # Test batch approval
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_approve',
                'selected_requests': [request.id],
            },
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful operation

    def test_error_handling_integration(self):
        """Test that error handling integrates with notification system"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for error handling
        self.assertContains(response, 'htmx:responseError')
        self.assertContains(response, 'htmx:sendError')

    def test_alpine_js_components_initialization(self):
        """Test that Alpine.js components are properly initialized"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:request_create'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check for Alpine.js component functions
        self.assertContains(response, 'function requestForm()')
        self.assertContains(response, 'updateItemInfo')
        self.assertContains(response, 'handleSubmit')

    def test_progressive_enhancement(self):
        """Test that the application works without JavaScript (progressive enhancement)"""
        self.client.login(username='dept_user', password='testpass123')
        
        # Test form submission without HTMX
        response = self.client.post(
            reverse('supply:request_create'),
            {
                'item': self.supply_item.id,
                'quantity': 5,
                'purpose': 'Test request without JavaScript'
            }
        )
        
        # Should redirect on successful submission
        self.assertEqual(response.status_code, 302)
        
        # Check that request was created
        self.assertTrue(
            SupplyRequest.objects.filter(
                purpose='Test request without JavaScript'
            ).exists()
        )

    def test_accessibility_features(self):
        """Test that accessibility features are present"""
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:dashboard'))

        self.assertEqual(response.status_code, 200)

        # Check for accessibility attributes
        self.assertContains(response, 'aria-hidden')
        self.assertContains(response, 'sr-only')  # Screen reader only content
