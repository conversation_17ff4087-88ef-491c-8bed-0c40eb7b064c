{% extends 'base_new.html' %}

{% block title %}Create Batch Request - MSRRMS{% endblock %}

{% block page_title %}Batch Request{% endblock %}
{% block mobile_title %}Batch Request{% endblock %}

{% block content %}
<div class="space-y-6" x-data="batchRequestApp()">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">Create Batch Request</h1>
                    <p class="text-blue-100 mt-1">Select multiple items for a single requisition</p>
                </div>
            </div>
            <div class="hidden md:flex items-center space-x-4">
                <div class="text-center">
                    <div class="text-2xl font-bold" x-text="selectedItems.length"></div>
                    <div class="text-blue-200 text-sm">Items Selected</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="px-6 py-5">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Search & Filter Items</h3>
            
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search Field -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Search Items
                    </label>
                    {{ search_form.search }}
                </div>

                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        Category
                    </label>
                    {{ search_form.category }}
                </div>

                <!-- In Stock Filter -->
                <div class="flex items-end">
                    <label class="flex items-center">
                        {{ search_form.in_stock_only }}
                        <span class="ml-2 text-sm text-gray-700">In Stock Only</span>
                    </label>
                </div>
            </form>
        </div>
    </div>

    <!-- Selected Items Cart -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100" x-show="selectedItems.length > 0" x-transition>
        <div class="px-6 py-5">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Selected Items</h3>
                <button @click="clearCart()" class="text-red-600 hover:text-red-800 text-sm font-medium">
                    Clear All
                </button>
            </div>
            
            <div class="space-y-3">
                <template x-for="item in selectedItems" :key="item.id">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center flex-1">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900" x-text="item.name"></h4>
                                <p class="text-sm text-gray-500" x-text="item.description"></p>
                                <p class="text-xs text-gray-400">
                                    Available: <span x-text="item.current_stock"></span> <span x-text="item.unit"></span>
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <!-- Quantity Controls -->
                            <div class="flex items-center space-x-2">
                                <button @click="decreaseQuantity(item.id)" 
                                        class="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                                    </svg>
                                </button>
                                <input type="number" 
                                       x-model="item.quantity" 
                                       @change="updateQuantity(item.id, $event.target.value)"
                                       :max="item.current_stock"
                                       min="1"
                                       class="w-16 text-center border-gray-300 rounded-md text-sm">
                                <button @click="increaseQuantity(item.id)" 
                                        class="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                </button>
                            </div>
                            
                            <!-- Remove Button -->
                            <button @click="removeFromCart(item.id)" 
                                    class="text-red-600 hover:text-red-800">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Items Grid -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="px-6 py-5">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Items</h3>
            
            <div id="items-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for item in items %}
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                    </svg>
                                </div>
                                <h4 class="font-medium text-gray-900">{{ item.name }}</h4>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">{{ item.description }}</p>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-500">{{ item.unit }}</span>
                                <span class="{% if item.is_low_stock %}text-red-600{% else %}text-green-600{% endif %} font-medium">
                                    {{ item.current_stock }} available
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-200">
                        <button @click="addToCart({{ item.id }}, '{{ item.name }}', '{{ item.description }}', {{ item.current_stock }}, '{{ item.unit }}')"
                                :disabled="isInCart({{ item.id }}) || {{ item.current_stock }} === 0"
                                :class="isInCart({{ item.id }}) ? 'bg-gray-300 cursor-not-allowed' : '{{ item.current_stock }} === 0 ? 'bg-red-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                                class="w-full px-3 py-2 text-sm font-medium text-white rounded-lg transition-colors duration-200">
                            <span x-show="isInCart({{ item.id }})">Added to Cart</span>
                            <span x-show="!isInCart({{ item.id }}) && {{ item.current_stock }} > 0">Add to Cart</span>
                            <span x-show="{{ item.current_stock }} === 0">Out of Stock</span>
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No items found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Request Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100" x-show="selectedItems.length > 0" x-transition>
        <div class="px-6 py-5">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Request Details</h3>
            
            <form method="post" @submit="submitBatchRequest">
                {% csrf_token %}
                
                <!-- Purpose Field -->
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Purpose <span class="text-red-500">*</span>
                    </label>
                    {{ batch_form.purpose }}
                    <p class="text-sm text-gray-500">{{ batch_form.purpose.help_text }}</p>
                </div>

                <!-- Hidden field for selected items -->
                <input type="hidden" name="selected_items" x-model="JSON.stringify(selectedItems)">

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row justify-end mt-6 space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                    <a href="{% url 'supply:dashboard' %}"
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Cancel
                    </a>
                    
                    <button type="submit" name="save_as_draft" value="true"
                            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                        </svg>
                        Save as Draft
                    </button>
                    
                    <button type="submit"
                            class="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function batchRequestApp() {
    return {
        selectedItems: [],
        
        addToCart(id, name, description, currentStock, unit) {
            if (!this.isInCart(id) && currentStock > 0) {
                this.selectedItems.push({
                    id: id,
                    name: name,
                    description: description,
                    current_stock: currentStock,
                    unit: unit,
                    quantity: 1
                });
            }
        },
        
        removeFromCart(id) {
            this.selectedItems = this.selectedItems.filter(item => item.id !== id);
        },
        
        isInCart(id) {
            return this.selectedItems.some(item => item.id === id);
        },
        
        clearCart() {
            this.selectedItems = [];
        },
        
        increaseQuantity(id) {
            const item = this.selectedItems.find(item => item.id === id);
            if (item && item.quantity < item.current_stock) {
                item.quantity++;
            }
        },
        
        decreaseQuantity(id) {
            const item = this.selectedItems.find(item => item.id === id);
            if (item && item.quantity > 1) {
                item.quantity--;
            }
        },
        
        updateQuantity(id, newQuantity) {
            const item = this.selectedItems.find(item => item.id === id);
            if (item) {
                const qty = parseInt(newQuantity);
                if (qty >= 1 && qty <= item.current_stock) {
                    item.quantity = qty;
                } else if (qty > item.current_stock) {
                    item.quantity = item.current_stock;
                    showNotification('warning', 'Quantity Adjusted', `Quantity reduced to available stock (${item.current_stock})`);
                } else {
                    item.quantity = 1;
                }
            }
        },
        
        submitBatchRequest(event) {
            if (this.selectedItems.length === 0) {
                event.preventDefault();
                showNotification('error', 'No Items Selected', 'Please select at least one item before submitting.');
                return false;
            }
            
            // The form will be submitted normally with the hidden field containing the selected items
            showNotification('info', 'Submitting Request', 'Processing your batch request...');
        }
    }
}
</script>
{% endblock %}
