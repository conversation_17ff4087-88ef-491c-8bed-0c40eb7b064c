from django.test import TestCase, Client, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from django.test.utils import override_settings
from django.db import connection
from django.test import TestCase
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction
import time
from django.db import transaction


class PerformanceTestCase(TestCase):
    """Performance tests for database queries and response times"""
    
    def setUp(self):
        """Set up test data for performance testing"""
        # Create users
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'General Services Office'
        self.gso_profile.save()
        
        # Create multiple department users
        self.dept_users = []
        for i in range(10):
            user = User.objects.create_user(
                username=f'dept_user_{i}',
                email=f'dept{i}@test.com',
                password='testpass123'
            )
            profile = UserProfile.objects.get(user=user)
            profile.role = 'DEPARTMENT'
            profile.department = f'Department {i}'
            profile.save()
            self.dept_users.append(user)
        
        # Create multiple supply items
        self.supply_items = []
        for i in range(50):
            item = SupplyItem.objects.create(
                name=f'Test Item {i}',
                description=f'Description for test item {i}',
                unit='pieces',
                current_stock=100 + i,
                minimum_stock=10
            )
            self.supply_items.append(item)
        
        # Create multiple supply requests
        self.supply_requests = []
        for i in range(100):
            user = self.dept_users[i % len(self.dept_users)]
            item = self.supply_items[i % len(self.supply_items)]
            
            request = SupplyRequest.objects.create(
                requester=user,
                department=user.userprofile.department,
                item=item,
                quantity=5 + (i % 10),
                purpose=f'Performance test request {i}',
                status='PENDING' if i % 3 == 0 else ('APPROVED' if i % 3 == 1 else 'RELEASED')
            )
            
            if request.status in ['APPROVED', 'RELEASED']:
                request.approved_by = self.gso_user
                request.approved_at = request.created_at
                
            if request.status == 'RELEASED':
                request.released_by = self.gso_user
                request.released_at = request.created_at
                
            request.save()
            self.supply_requests.append(request)
        
        self.client = Client()

    def test_dashboard_query_performance(self):
        """Test dashboard loading performance with large datasets"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Reset query count
        with self.assertNumQueries(None) as context:
            start_time = time.time()
            response = self.client.get(reverse('supply:gso_dashboard'))
            end_time = time.time()
        
        # Check response time
        response_time = end_time - start_time
        self.assertLess(response_time, 2.0, f"Dashboard should load in under 2 seconds, took {response_time:.2f}s")
        
        # Check response status
        self.assertEqual(response.status_code, 200)
        
        # Check query count (should be reasonable)
        query_count = len(context.captured_queries)
        self.assertLess(query_count, 20, f"Dashboard should use fewer than 20 queries, used {query_count}")

    def test_request_list_pagination_performance(self):
        """Test request list performance with pagination"""
        self.client.login(username='gso_user', password='testpass123')
        
        with self.assertNumQueries(None) as context:
            start_time = time.time()
            response = self.client.get(reverse('supply:gso_dashboard'), {'page': 1})
            end_time = time.time()
        
        response_time = end_time - start_time
        self.assertLess(response_time, 1.0, f"Paginated list should load in under 1 second, took {response_time:.2f}s")
        
        self.assertEqual(response.status_code, 200)
        
        # Query count should be reasonable even with pagination
        query_count = len(context.captured_queries)
        self.assertLess(query_count, 15, f"Paginated list should use fewer than 15 queries, used {query_count}")

    def test_search_performance(self):
        """Test search functionality performance"""
        self.client.login(username='gso_user', password='testpass123')
        
        search_queries = ['test', 'item', 'request', 'department']
        
        for query in search_queries:
            with self.assertNumQueries(None) as context:
                start_time = time.time()
                response = self.client.get(reverse('supply:global_search'), {'q': query})
                end_time = time.time()
            
            response_time = end_time - start_time
            self.assertLess(response_time, 0.5, f"Search for '{query}' should complete in under 0.5 seconds, took {response_time:.2f}s")
            
            self.assertEqual(response.status_code, 200)
            
            # Search should be efficient
            query_count = len(context.captured_queries)
            self.assertLess(query_count, 10, f"Search should use fewer than 10 queries, used {query_count}")

    def test_bulk_operations_performance(self):
        """Test bulk operations performance"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Get pending requests for bulk operation
        pending_requests = SupplyRequest.objects.filter(status='PENDING')[:20]
        request_ids = [str(req.id) for req in pending_requests]
        
        with self.assertNumQueries(None) as context:
            start_time = time.time()
            response = self.client.post(reverse('supply:batch_operations'), {
                'action': 'bulk_approve',
                'selected_requests': request_ids,
            })
            end_time = time.time()
        
        response_time = end_time - start_time
        self.assertLess(response_time, 3.0, f"Bulk operation should complete in under 3 seconds, took {response_time:.2f}s")
        
        self.assertEqual(response.status_code, 302)
        
        # Bulk operations should be efficient
        query_count = len(context.captured_queries)
        # Should not be O(n) queries for n items
        self.assertLess(query_count, len(request_ids) * 2, f"Bulk operation should be efficient, used {query_count} queries for {len(request_ids)} items")

    def test_report_generation_performance(self):
        """Test report generation performance"""
        self.client.login(username='gso_user', password='testpass123')
        
        reports = [
            reverse('supply:requests_report'),
            reverse('supply:departmental_usage_report'),
            reverse('supply:inventory_report'),
        ]
        
        for report_url in reports:
            with self.assertNumQueries(None) as context:
                start_time = time.time()
                response = self.client.get(report_url)
                end_time = time.time()
            
            response_time = end_time - start_time
            self.assertLess(response_time, 5.0, f"Report {report_url} should generate in under 5 seconds, took {response_time:.2f}s")
            
            self.assertEqual(response.status_code, 200)
            
            # Reports should use efficient queries
            query_count = len(context.captured_queries)
            self.assertLess(query_count, 25, f"Report should use fewer than 25 queries, used {query_count}")

    def test_inventory_operations_performance(self):
        """Test inventory operations performance"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test inventory list
        with self.assertNumQueries(None) as context:
            start_time = time.time()
            response = self.client.get(reverse('supply:inventory'))
            end_time = time.time()
        
        response_time = end_time - start_time
        self.assertLess(response_time, 2.0, f"Inventory list should load in under 2 seconds, took {response_time:.2f}s")
        
        self.assertEqual(response.status_code, 200)
        
        # Test stock adjustment
        item = self.supply_items[0]
        with self.assertNumQueries(None) as context:
            start_time = time.time()
            response = self.client.post(reverse('supply:inventory_adjust', args=[item.id]), {
                'adjustment_type': 'IN',
                'quantity': 10,
                'remarks': 'Performance test adjustment'
            })
            end_time = time.time()
        
        response_time = end_time - start_time
        self.assertLess(response_time, 1.0, f"Stock adjustment should complete in under 1 second, took {response_time:.2f}s")

    def test_concurrent_request_creation(self):
        """Test performance under concurrent request creation"""
        # Simulate multiple users creating requests simultaneously
        users = self.dept_users[:5]  # Use 5 users
        
        start_time = time.time()
        
        for user in users:
            self.client.login(username=user.username, password='testpass123')
            
            for i in range(5):  # Each user creates 5 requests
                response = self.client.post(reverse('supply:request_create'), {
                    'item': self.supply_items[i].id,
                    'quantity': 3,
                    'purpose': f'Concurrent test request {i} by {user.username}'
                })
                self.assertEqual(response.status_code, 302)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 25 requests should be created reasonably quickly
        self.assertLess(total_time, 10.0, f"Creating 25 requests should take under 10 seconds, took {total_time:.2f}s")

    def test_database_query_optimization(self):
        """Test that database queries are optimized"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test select_related and prefetch_related usage
        with self.assertNumQueries(None) as context:
            # This should use select_related to avoid N+1 queries
            requests = list(SupplyRequest.objects.select_related('item', 'requester', 'approved_by')[:10])
            
            # Access related objects
            for request in requests:
                _ = request.item.name
                _ = request.requester.username
                if request.approved_by:
                    _ = request.approved_by.username
        
        # Should use minimal queries due to select_related
        query_count = len(context.captured_queries)
        self.assertLess(query_count, 5, f"Optimized query should use fewer than 5 queries, used {query_count}")

    def test_widget_api_performance(self):
        """Test dashboard widget API performance"""
        self.client.login(username='gso_user', password='testpass123')
        
        widgets = ['statistics', 'recent_activity', 'low_stock_alerts', 'pending_approvals']
        
        for widget in widgets:
            with self.assertNumQueries(None) as context:
                start_time = time.time()
                response = self.client.get(reverse('supply:dashboard_widgets'), {'widget': widget})
                end_time = time.time()
            
            response_time = end_time - start_time
            self.assertLess(response_time, 0.5, f"Widget '{widget}' should load in under 0.5 seconds, took {response_time:.2f}s")
            
            self.assertEqual(response.status_code, 200)
            
            # Widget APIs should be fast
            query_count = len(context.captured_queries)
            self.assertLess(query_count, 8, f"Widget API should use fewer than 8 queries, used {query_count}")

    def test_memory_usage_patterns(self):
        """Test memory usage patterns (basic check)"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test that large querysets don't load everything into memory
        # This is a basic test - in production, you'd use memory profiling tools
        
        # Use iterator() for large querysets
        large_queryset = SupplyRequest.objects.all()
        
        # This should not load all objects into memory at once
        count = 0
        for request in large_queryset.iterator():
            count += 1
            if count > 10:  # Just test a few
                break
        
        self.assertGreater(count, 0)

    def test_cache_effectiveness(self):
        """Test caching effectiveness (if implemented)"""
        # Note: This application doesn't implement caching yet,
        # but this is where you'd test cache hit rates and performance improvements
        
        self.client.login(username='gso_user', password='testpass123')
        
        # First request (cache miss)
        start_time = time.time()
        response1 = self.client.get(reverse('supply:dashboard_widgets'), {'widget': 'statistics'})
        first_request_time = time.time() - start_time
        
        # Second request (should be faster if cached)
        start_time = time.time()
        response2 = self.client.get(reverse('supply:dashboard_widgets'), {'widget': 'statistics'})
        second_request_time = time.time() - start_time
        
        self.assertEqual(response1.status_code, 200)
        self.assertEqual(response2.status_code, 200)
        
        # Note: Without actual caching, this test just ensures both requests work
        # In a real cached system, second_request_time should be significantly less

    def test_index_usage_simulation(self):
        """Test that database indexes are being used effectively"""
        # This is a simulation - in production, you'd analyze query execution plans
        
        # Test queries that should use indexes
        test_queries = [
            # Should use index on status
            SupplyRequest.objects.filter(status='PENDING'),
            # Should use index on created_at
            SupplyRequest.objects.filter(created_at__gte='2024-01-01'),
            # Should use index on requester
            SupplyRequest.objects.filter(requester=self.dept_users[0]),
        ]
        
        for queryset in test_queries:
            start_time = time.time()
            list(queryset[:10])  # Force evaluation
            query_time = time.time() - start_time
            
            # Indexed queries should be fast
            self.assertLess(query_time, 0.1, f"Indexed query should be fast, took {query_time:.3f}s")
